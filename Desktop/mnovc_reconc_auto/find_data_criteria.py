#!/usr/bin/env python3
"""
Find the correct date range and IoT/Non-IoT criteria for StarRocks data.
"""

import pymysql
import pandas as pd
import os
from dotenv import load_dotenv
from datetime import datetime, <PERSON><PERSON><PERSON>

def find_data_criteria():
    """Find the correct criteria for data retrieval."""
    
    load_dotenv()
    
    host = os.getenv('STARROCKS_HOST')
    port = int(os.getenv('STARROCKS_PORT', '9030'))
    user = os.getenv('STARROCKS_USER')
    password = os.getenv('STARROCKS_PASSWORD')
    database = os.getenv('STARROCKS_DATABASE')
    
    print("🔍 Finding Data Criteria for StarRocks")
    print("=" * 50)
    
    try:
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4',
            autocommit=True
        )
        cursor = connection.cursor()
        
        # 1. Find available date ranges
        print("1️⃣ Available Date Ranges:")
        cursor.execute("""
            SELECT 
                MIN(FILE_DATE) as min_date,
                MAX(FILE_DATE) as max_date,
                COUNT(DISTINCT FILE_DATE) as unique_dates,
                COUNT(*) as total_records
            FROM MVNOC_SNAPSHOT
        """)
        date_info = cursor.fetchone()
        print(f"   Date range: {date_info[0]} to {date_info[1]}")
        print(f"   Unique dates: {date_info[2]}")
        print(f"   Total records: {date_info[3]:,}")
        
        # 2. Recent dates with data
        print(f"\n2️⃣ Recent Dates with Data:")
        cursor.execute("""
            SELECT FILE_DATE, COUNT(*) as record_count
            FROM MVNOC_SNAPSHOT 
            WHERE FILE_DATE >= DATE_SUB(CURDATE(), INTERVAL 60 DAY)
            GROUP BY FILE_DATE 
            ORDER BY FILE_DATE DESC 
            LIMIT 10
        """)
        recent_dates = cursor.fetchall()
        for date_row in recent_dates:
            print(f"   {date_row[0]}: {date_row[1]:,} records")
        
        # 3. Analyze BILLING_CODE and WPS patterns
        print(f"\n3️⃣ BILLING_CODE Analysis:")
        cursor.execute("""
            SELECT BILLING_CODE, COUNT(*) as count
            FROM MVNOC_SNAPSHOT 
            WHERE BILLING_CODE IS NOT NULL
            GROUP BY BILLING_CODE 
            ORDER BY count DESC 
            LIMIT 15
        """)
        billing_codes = cursor.fetchall()
        for code_row in billing_codes:
            print(f"   {code_row[0]}: {code_row[1]:,} records")
        
        print(f"\n4️⃣ WPS Analysis:")
        cursor.execute("""
            SELECT WPS, COUNT(*) as count
            FROM MVNOC_SNAPSHOT 
            WHERE WPS IS NOT NULL
            GROUP BY WPS 
            ORDER BY count DESC 
            LIMIT 15
        """)
        wps_values = cursor.fetchall()
        for wps_row in wps_values:
            print(f"   {wps_row[0]}: {wps_row[1]:,} records")
        
        # 5. Test with a recent date that has data
        if recent_dates:
            test_date = recent_dates[0][0]
            print(f"\n5️⃣ Testing with Recent Date: {test_date}")
            
            # Test basic query
            cursor.execute("""
                SELECT 
                    PRIMARY_RESOURCE as SUBSCRIBER_ID,
                    SUBSCRIBER_STATE,
                    BILLING_CODE,
                    WPS,
                    COUNT(*) as count
                FROM MVNOC_SNAPSHOT 
                WHERE FILE_DATE = %s
                AND SUBSCRIBER_STATE = 'ACTIVE'
                GROUP BY PRIMARY_RESOURCE, SUBSCRIBER_STATE, BILLING_CODE, WPS
                LIMIT 10
            """, (test_date,))
            
            test_data = cursor.fetchall()
            cols = [desc[0] for desc in cursor.description]
            
            if test_data:
                test_df = pd.DataFrame(test_data, columns=cols)
                print(f"   ✅ Found {len(test_data)} active subscribers on {test_date}")
                print(f"   Sample data:")
                print(test_df.head().to_string(index=False))
                
                # Suggest IoT criteria
                print(f"\n6️⃣ Suggested IoT/Non-IoT Criteria:")
                
                # Look for patterns that might indicate IoT
                iot_patterns = []
                noniot_patterns = []
                
                for _, row in test_df.iterrows():
                    billing_code = str(row['BILLING_CODE']).upper()
                    wps = str(row['WPS']).upper()
                    
                    # Common IoT indicators
                    if any(indicator in billing_code for indicator in ['IOT', 'M2M', 'DEVICE']):
                        iot_patterns.append(f"BILLING_CODE = '{row['BILLING_CODE']}'")
                    elif any(indicator in wps for indicator in ['IOT', 'M2M', 'DEVICE']):
                        iot_patterns.append(f"WPS = '{row['WPS']}'")
                    else:
                        noniot_patterns.append(f"BILLING_CODE = '{row['BILLING_CODE']}' AND WPS = '{row['WPS']}'")
                
                if iot_patterns:
                    print(f"   IoT patterns found: {set(iot_patterns)}")
                else:
                    print(f"   ⚠️  No obvious IoT patterns found in sample data")
                    print(f"   💡 You may need to:")
                    print(f"      - Check with your team about IoT identification rules")
                    print(f"      - Use TENANT_ID to distinguish IoT vs Non-IoT")
                    print(f"      - Use a different table or column")
                
                # 7. Test with TENANT_ID approach
                print(f"\n7️⃣ TENANT_ID Analysis:")
                cursor.execute("""
                    SELECT TENANT_ID, COUNT(*) as count
                    FROM MVNOC_SNAPSHOT 
                    WHERE FILE_DATE = %s
                    AND SUBSCRIBER_STATE = 'ACTIVE'
                    GROUP BY TENANT_ID 
                    ORDER BY count DESC
                """, (test_date,))
                
                tenant_data = cursor.fetchall()
                print(f"   Tenant distribution on {test_date}:")
                for tenant_row in tenant_data:
                    print(f"      Tenant {tenant_row[0]}: {tenant_row[1]:,} active subscribers")
                
                print(f"\n💡 Recommendations:")
                print(f"   1. Use date: {test_date} (or recent dates with data)")
                print(f"   2. Consider using TENANT_ID to distinguish IoT vs Non-IoT")
                print(f"   3. If specific tenants are IoT, update the SQL queries accordingly")
                print(f"   4. Test with a smaller date range first")
                
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ Error analyzing data: {e}")

if __name__ == "__main__":
    find_data_criteria()
