{"asctime": "2025-10-08 16:39:14,591064", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Telgoo Recon job started at 2025-10-08 16:39:14.", "job_id": "20251008_163914"}
{"asctime": "2025-10-08 16:39:14,591133", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Dates computed: Today=2025-10-08, Usage Date=2025-10-07, File Date=2025-10-08", "job_id": "20251008_163914"}
{"asctime": "2025-10-08 16:39:14,591308", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Dates computed: Start Date=2025-05-02, End Date=2025-06-01", "job_id": "20251008_163914"}
{"asctime": "2025-10-08 16:39:15,100218", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Loaded data: MVNO (        TENANT_ID     SUBSCRIBER_ID  PRIMARY_RESOURCE SUBSCRIBER_STATE  ... DEACTIVATED_DATE        NAME NO_OF_ACTIVE_DAYS  BILLABLE_DAYS\n0            1010  2251799813871053        5612709465           ACTIVE  ...               \\N  scriptsave               631             31\n1            1010  2251799813882186        2104458655           ACTIVE  ...               \\N  scriptsave               627             31\n2            1010  2251799813882263        2104738422           ACTIVE  ...               \\N  scriptsave               627             31\n3            1010  2251799813882266        2104739829           ACTIVE  ...               \\N  scriptsave               627             31\n4            1010  2251799813882273        2105352132           ACTIVE  ...               \\N  scriptsave               627             31\n...           ...               ...               ...              ...  ...              ...         ...               ...            ...\n174475       1009  2251799817663371        9012463692           ACTIVE  ...               \\N        boom                 2              1\n174476       1009  2251799817663375        9174179198           ACTIVE  ...               \\N        boom                 2              1\n174477       1009  2251799817663383        3473611465           ACTIVE  ...               \\N        boom                 2              1\n174478       1009  2251799817663389        3513451736           ACTIVE  ...               \\N        boom                 2              1\n174479       1009  2251799817663407        9174179506           ACTIVE  ...               \\N        boom                 2              1\n\n[174480 rows x 9 columns]) and Telgoo (        TENANT_ID     SUBSCRIBER_ID  PRIMARY_RESOURCE  ... DEACTIVATED_DATE                 NAME NO_OF_ACTIVE_DAYS\n0               7  2251799813685790        6892808081  ...               \\N     MVNOConnect Demo               742\n1               7  2251799813685829        6892808252  ...               \\N     MVNOConnect Demo               738\n2               7  2251799813685830        6892808253  ...               \\N     MVNOConnect Demo               738\n3               7  2251799813688393        6892963035  ...               \\N     MVNOConnect Demo               703\n4               7  2251799813688394        6892963044  ...               \\N     MVNOConnect Demo               703\n...           ...               ...               ...  ...              ...                  ...               ...\n334181       1016  2251799817663286        2135512232  ...               \\N  expertcommunication                 2\n334182       1006  2251799817663307        9297120300  ...               \\N             USMobile                 2\n334183       1006  2251799817663315        6466109149  ...               \\N             USMobile                 2\n334184       1006  2251799817663319        7866455612  ...               \\N             USMobile                 2\n334185       1006  2251799817663411        6692909888  ...               \\N             USMobile                 2\n\n[334186 rows x 8 columns])", "job_id": "20251008_163914"}
{"asctime": "2025-10-08 16:39:17,586639", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Successfully connected to StarRocks.", "job_id": "20251008_163914"}
{"asctime": "2025-10-08 16:39:21,794778", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Retrieved IOT Snapshot data for dates 2025-05-02 to 2025-06-01. Total rows: 17075", "job_id": "20251008_163914"}
