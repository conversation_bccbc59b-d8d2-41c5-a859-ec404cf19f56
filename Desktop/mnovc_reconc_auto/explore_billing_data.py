#!/usr/bin/env python3
"""
Explore billing_data database structure and find the equivalent of stored procedures.
"""

import pymysql
import os
from dotenv import load_dotenv

def explore_billing_data():
    """Explore billing_data database structure."""
    
    load_dotenv()
    
    host = os.getenv('STARROCKS_HOST')
    port = int(os.getenv('STARROCKS_PORT', '9030'))
    user = os.getenv('STARROCKS_USER')
    password = os.getenv('STARROCKS_PASSWORD')
    database = os.getenv('STARROCKS_DATABASE')
    
    print(f"🔍 Exploring '{database}' Database")
    print("=" * 50)
    
    try:
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4',
            autocommit=True
        )
        cursor = connection.cursor()
        
        # 1. Show all tables
        print("1️⃣ Tables in billing_data:")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        for table in tables:
            print(f"   📋 {table[0]}")
        
        # 2. Examine key tables that might contain our data
        key_tables = ['MVNOC_SNAPSHOT', 'NEXUS_MVNOC_SNAPSHOT', 'CDR_DATA']
        
        for table_name in key_tables:
            if any(table[0] == table_name for table in tables):
                print(f"\n2️⃣ Examining table: {table_name}")
                
                # Get table structure
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()
                print(f"   Columns ({len(columns)}):")
                for col in columns:
                    print(f"      • {col[0]} ({col[1]})")
                
                # Get sample data
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   Total rows: {count:,}")
                
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    sample_data = cursor.fetchall()
                    print(f"   Sample data (first 3 rows):")
                    for i, row in enumerate(sample_data, 1):
                        print(f"      Row {i}: {row[:5]}...")  # Show first 5 columns
        
        # 3. Look for IoT and Non-IoT related data
        print(f"\n3️⃣ Searching for IoT/Non-IoT patterns:")
        
        # Check if there are columns that might indicate IoT vs Non-IoT
        for table_name in [t[0] for t in tables]:
            try:
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()
                column_names = [col[0].lower() for col in columns]
                
                # Look for IoT-related columns
                iot_indicators = ['iot', 'device_type', 'service_type', 'plan_type']
                found_indicators = [ind for ind in iot_indicators if any(ind in col for col in column_names)]
                
                if found_indicators:
                    print(f"   📋 {table_name}: Found IoT indicators: {found_indicators}")
                    
                    # Check for SUBSCRIBER_ID column
                    if 'subscriber_id' in column_names:
                        print(f"      ✅ Has SUBSCRIBER_ID column")
                    
                    # Check for date-related columns
                    date_columns = [col for col in column_names if any(date_word in col for date_word in ['date', 'time', 'day'])]
                    if date_columns:
                        print(f"      📅 Date columns: {date_columns}")
                        
            except Exception as e:
                print(f"   ❌ Error examining {table_name}: {e}")
        
        # 4. Try to understand the data structure for creating equivalent queries
        print(f"\n4️⃣ Data Structure Analysis:")
        
        # Look for the main snapshot table
        snapshot_tables = [t[0] for t in tables if 'snapshot' in t[0].lower()]
        for snapshot_table in snapshot_tables:
            print(f"\n   Analyzing {snapshot_table}:")
            try:
                cursor.execute(f"DESCRIBE {snapshot_table}")
                columns = cursor.fetchall()
                
                # Look for key columns we need
                key_columns = ['SUBSCRIBER_ID', 'ACTIVE_DAYS', 'SUBSCRIBER_STATE', 'SERVICE_TYPE', 'PLAN_TYPE']
                found_columns = []
                
                for col in columns:
                    col_name = col[0].upper()
                    if col_name in key_columns:
                        found_columns.append(col_name)
                        print(f"      ✅ Found: {col_name} ({col[1]})")
                
                # Check for date range filtering capability
                date_columns = [col[0] for col in columns if any(word in col[0].lower() for word in ['date', 'time', 'day', 'period'])]
                if date_columns:
                    print(f"      📅 Date filtering columns: {date_columns}")
                
                # Sample query to understand data
                if found_columns:
                    sample_query = f"SELECT {', '.join(found_columns[:3])} FROM {snapshot_table} LIMIT 5"
                    cursor.execute(sample_query)
                    sample_data = cursor.fetchall()
                    print(f"      📊 Sample data:")
                    for row in sample_data:
                        print(f"         {row}")
                        
            except Exception as e:
                print(f"      ❌ Error analyzing {snapshot_table}: {e}")
        
        cursor.close()
        connection.close()
        
        print(f"\n📋 Next Steps:")
        print(f"   1. Identify the correct table(s) for IoT and Non-IoT data")
        print(f"   2. Create SQL queries to replace the stored procedures")
        print(f"   3. Update the application to use direct SQL instead of CALL statements")
        
    except Exception as e:
        print(f"❌ Error exploring database: {e}")

if __name__ == "__main__":
    explore_billing_data()
