"""
Telgoo Reconciliation Automation System

This script performs automated reconciliation between MVNOC billing data and Telgoo invoice data.
It processes both IoT and Non-IoT subscribers, generates Excel reports with cost calculations,
and provides comprehensive logging for monitoring and debugging.

Author: Vyomakesh
Company: MVNOC
Version: 1.1
Last Updated: July 2025

Main Features:
- Retrieves subscriber data from StarRocks database
- Loads Telgoo billing CSV files
- Performs data reconciliation and cost calculations
- Generates detailed Excel reports with summary and raw data
- Implements robust error handling and structured logging
"""

# ============================================================================
# IMPORTS AND DEPENDENCIES
# ============================================================================

import pandas as pd                                    # Data manipulation and analysis
import paramiko.sftp_client                           # SFTP client for file transfers
import numpy as np                                    # Numerical operations
from datetime import date, datetime, timedelta       # Date and time handling
from dateutil.relativedelta import relativedelta     # Advanced date calculations
from struct_logger import *                          # Custom structured logging module
from dotenv import load_dotenv                       # Environment variable management
import boto3                                         # AWS SDK (for future S3 integration)
from concurrent.futures import ThreadPoolExecutor, as_completed  # Parallel processing
import pymysql                                       # StarRocks database connectivity (MySQL protocol)
import paramiko                                      # SSH/SFTP operations
import zipfile                                       # Archive file handling
import os                                           # Operating system interface

# ============================================================================
# CONFIGURATION AND CONSTANTS
# ============================================================================

# Load environment variables from .env file for secure configuration management
load_dotenv()

# Business logic constants for data filtering
TENANT_IDS = ['8', '1023', '1010', '1009']          # Valid tenant IDs for processing
IOT_BILL_CODES = ['61100017', '82200022', '81800040', '81600011']  # IoT-specific billing codes

# AWS configuration loading with error handling
# Note: AWS integration is prepared for future S3 file storage requirements
try:
    aws_access_key_id = os.getenv('AWS_ACCESS_KEY_ID')
    aws_secret_access_key = os.getenv('AWS_SECRET_ACCESS_KEY')
    Bucket = os.getenv('AWS_BUCKET_NAME')
except Exception as e:
    # Logger may not be initialized yet, so this might not work in global scope
    # Error will be handled when logger is available
    pass


# ============================================================================
# IOT RECONCILIATION FUNCTIONS
# ============================================================================

def iot_recon(IOT_SNAPSHOT_BILLABLE, telgoo_iot, logger):
    """
    Perform IoT reconciliation between Telgoo billing data and MVNOC snapshot data.

    This function:
    1. Calculates total billable days from Telgoo data
    2. Converts subscriber IDs to consistent format
    3. Merges Telgoo and MVNOC data on subscriber ID
    4. Calculates total active days from MVNOC data
    5. Returns merged dataset and summary metrics for reporting

    Args:
        IOT_SNAPSHOT_BILLABLE (DataFrame): MVNOC IoT subscriber data from StarRocks
        telgoo_iot (DataFrame): Telgoo IoT billing data from CSV
        logger (Logger): Structured logger instance for error reporting

    Returns:
        tuple: (
            MSISDNWISE_iot (DataFrame): Merged dataset with all subscriber details,
            mvnoc_billable_days (int): Total active days from MVNOC data,
            telgoo_billable_days (int): Total billable days from Telgoo data
        )
    """

    # Step 1: Calculate total billable days from Telgoo data
    try:
        # Sum all billable days from Telgoo data for cost calculation
        telgoo_billable_days = telgoo_iot['BILLABLE_DAYS'].sum()
        logger.info("Filtered Telgoo IoT billable records: %d rows", telgoo_billable_days)
    except Exception as e:
        # Handle missing columns or data type issues
        logger.exception("Error filtering IoT data: %s", e)
        # Default to 0 if calculation fails
        telgoo_billable_days = 0

    # Step 2: Process and merge snapshot data with Telgoo data
    try:
        # Convert subscriber IDs to consistent numeric format for proper joining
        telgoo_iot['SUBSCRIBER_ID'] = telgoo_iot['SUBSCRIBER_ID'].astype(np.int64)
        IOT_SNAPSHOT_BILLABLE['SUBSCRIBER_ID'] = IOT_SNAPSHOT_BILLABLE['SUBSCRIBER_ID'].astype(np.int64)

        # Merge datasets using left join to keep all Telgoo records
        # This allows identification of subscribers missing from MVNOC data
        MSISDNWISE_iot = pd.merge(telgoo_iot, IOT_SNAPSHOT_BILLABLE, on='SUBSCRIBER_ID', how='left')

        # Calculate total active days from MVNOC data for comparison with Telgoo
        mvnoc_billable_days = MSISDNWISE_iot['ACTIVE_DAYS'].sum()
        logger.info("Filtered snapshot IoT billable records: %d rows", mvnoc_billable_days)
    except Exception as e:
        # Handle data conversion or merge errors
        logger.exception("Error filtering snapshot IoT data: %s", e)
        # Create empty DataFrame and set defaults if processing fails
        MSISDNWISE_iot = pd.DataFrame()
        mvnoc_billable_days = 0

    # Return processed data and metrics for report generation
    return MSISDNWISE_iot, mvnoc_billable_days, telgoo_billable_days

   

def non_iot_recon(non_iot_MSISDN, telgoo_non_iot, logger):
    """
    Perform Non-IoT reconciliation between Telgoo billing data and MVNOC snapshot data.

    This function:
    1. Counts total subscribers from Telgoo data
    2. Converts subscriber IDs to consistent format
    3. Merges Telgoo and MVNOC data on subscriber ID
    4. Calculates active MVNOC subscribers with complex filtering
    5. Returns merged dataset and summary metrics for reporting

    Args:
        non_iot_MSISDN (DataFrame): MVNOC Non-IoT subscriber data from StarRocks
        telgoo_non_iot (DataFrame): Telgoo Non-IoT billing data from CSV
        logger (Logger): Structured logger instance for error reporting

    Returns:
        tuple: (
            telgoo_noniot_users (int): Total subscribers from Telgoo data,
            MSISDNWISE_non_iot (DataFrame): Merged dataset with all subscriber details,
            mvnoc_noniot_users (int): Total active subscribers from MVNOC data
        )
    """

    # Step 1: Calculate total Non-IoT users from Telgoo data
    try:
        # Count total subscribers in Telgoo data
        telgoo_noniot_users = len(telgoo_non_iot['SUBSCRIBER_ID'])
        logger.info("Calculated Telgoo non-IoT users: %d", telgoo_noniot_users)
    except Exception as e:
        # Handle missing columns or data type issues
        logger.exception("Error calculating Telgoo non-IoT users: %s", e)
        # Default to 0 if calculation fails
        telgoo_noniot_users = 0

    # Step 2: Process and merge snapshot data with Telgoo data
    try:
        # Convert subscriber IDs to consistent integer format for proper joining
        telgoo_non_iot['SUBSCRIBER_ID'] = telgoo_non_iot['SUBSCRIBER_ID'].astype(int)
        non_iot_MSISDN['SUBSCRIBER_ID'] = non_iot_MSISDN['SUBSCRIBER_ID'].astype(int)

        # Merge datasets using left join to keep all Telgoo records
        # This allows identification of subscribers missing from MVNOC data
        MSISDNWISE_non_iot = pd.merge(telgoo_non_iot, non_iot_MSISDN, on='SUBSCRIBER_ID', how='left')

        # Debug: Check available columns in the merged DataFrame
        logger.info("Available columns in MSISDNWISE_non_iot: %s", list(MSISDNWISE_non_iot.columns))

        # Complex filtering to count only valid active subscribers:
        # Check if SUBSCRIBER_STATE column exists (it might be named differently after merge)
        if 'SUBSCRIBER_STATE' in MSISDNWISE_non_iot.columns:
            # Exclude records that are marked as ACTIVE but have null ACTIVE_DAYS
            valid_records = ~((MSISDNWISE_non_iot['SUBSCRIBER_STATE'] == 'ACTIVE') &
                             (MSISDNWISE_non_iot['ACTIVE_DAYS'].isnull()))
            mvnoc_noniot_users = len(MSISDNWISE_non_iot[valid_records]['ACTIVE_DAYS'])
        elif 'SUBSCRIBER_STATE_y' in MSISDNWISE_non_iot.columns:
            # Handle case where column was renamed during merge (from StarRocks data)
            valid_records = ~((MSISDNWISE_non_iot['SUBSCRIBER_STATE_y'] == 'ACTIVE') &
                             (MSISDNWISE_non_iot['ACTIVE_DAYS'].isnull()))
            mvnoc_noniot_users = len(MSISDNWISE_non_iot[valid_records]['ACTIVE_DAYS'])
        else:
            # Fallback: count all records with non-null ACTIVE_DAYS
            logger.warning("SUBSCRIBER_STATE column not found, using fallback counting method")
            mvnoc_noniot_users = len(MSISDNWISE_non_iot[MSISDNWISE_non_iot['ACTIVE_DAYS'].notna()])

        logger.info("Calculated MVNOC non-IoT users: %d", mvnoc_noniot_users)

    except Exception as e:
        # Handle data conversion or merge errors
        logger.exception("Error filtering snapshot non-IoT data: %s", e)
        # Create empty DataFrame and set defaults if processing fails
        MSISDNWISE_non_iot = pd.DataFrame()
        mvnoc_noniot_users = 0

    # Return processed data and metrics for report generation
    return telgoo_noniot_users, MSISDNWISE_non_iot, mvnoc_noniot_users

  

# ============================================================================
# DATE CALCULATION UTILITIES
# ============================================================================

def compute_window(today: date = None):
    """
    Calculate the billing period window for data processing.

    Business Logic:
    - Billing periods run from the 2nd of one month to the 1st of the next month
    - This avoids month-end processing conflicts and ensures complete data availability
    - The function automatically determines the previous month's billing period

    Date Calculation Logic:
    1. Get the first day of the current month
    2. Calculate the first day of the previous month
    3. Set start_date to the 2nd day of the previous month
    4. Set end_date to the 1st day of the current month

    Args:
        today (date, optional): Reference date for calculation. Defaults to current date.

    Returns:
        tuple: (start_date, end_date) as formatted strings ('YYYY-MM-DD')

    Example:
        If today = 2024-07-25:
        - start_date = '2024-06-02' (2nd of June)
        - end_date = '2024-07-01' (1st of July)

        This captures the complete June billing period.
    """
    # Use current date if no specific date provided
    if today is None:
        today = date.today()

    # Step 1: Get first day of the current month
    # This normalizes any input date to month boundaries
    first_of_curr = today.replace(day=1)

    # Step 2: Calculate first day of previous month
    # relativedelta handles month/year boundaries correctly
    first_of_prev = first_of_curr - relativedelta(months=1)

    # Step 3: Set start_date to 2nd day of previous month
    # Avoids month-end processing issues and ensures data completeness
    start_date = first_of_prev.replace(day=2)
    start_date = start_date.strftime('%Y-%m-%d')

    # Step 4: Set end_date to 1st day of current month
    # Creates a clean monthly boundary for the billing period
    end_date = first_of_curr
    end_date = end_date.strftime('%Y-%m-%d')

    return start_date, end_date


def make_non_iot_results_excel(MSISDNWISE: pd.DataFrame, mvnoc_billable: int, telgoo_billable: int, month_name: str, year: str, logger):
    """
    Generate comprehensive Excel report for Non-IoT reconciliation with tiered pricing model.

    This function creates a detailed Excel workbook containing:
    1. Summary Sheet: Financial calculations with tiered pricing formulas
    2. MSISDNWISE Sheet: Complete subscriber-level data for analysis

    Non-IoT Pricing Model:
    - First 25,000 users: $0.25 per user
    - Additional users (25,000+): $0.24 per user
    - Automatic calculation of cost differences between Telgoo and MVNOC

    Args:
        MSISDNWISE (DataFrame): Merged subscriber data with reconciliation details
        mvnoc_billable (int): Total billable users from MVNOC data
        telgoo_billable (int): Total billable users from Telgoo data
        month_name (str): Three-letter month abbreviation (e.g., 'JUN')
        year (str): Four-digit year (e.g., '2025')
        logger (Logger): Structured logger for error reporting

    Returns:
        str: Full file path of the generated Excel report, or None if creation fails
    """
    # Generate standardized filename with month and year for easy identification
    filename = f"Telgoo_reconciliation_NonIoT_{month_name}{year}.xlsx"

    # Use hardcoded path to avoid environment variable issues
    # TODO: Make this configurable through environment variables
    output_dir = "/Users/<USER>/Desktop/mnovc_reconc_auto/final_report"

    # Ensure output directory exists with comprehensive error handling
    try:
        os.makedirs(output_dir, exist_ok=True)
    except OSError as e:
        logger.error("Error creating output directory %s: %s", output_dir, e)
        return None

    # Construct full file path for the Excel report
    file_path = os.path.join(output_dir, filename)

    # Create Excel workbook using XlsxWriter engine for advanced formula support
    with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:

        # ===== SUMMARY SHEET CREATION =====
        # Create workbook and worksheet objects for direct cell manipulation
        wb = writer.book
        ws = wb.add_worksheet("summary sheet")
        writer.sheets["summary sheet"] = ws

        # Define header labels and their positions for the summary table
        # Layout creates a comparison table: Telgoo vs MVNOC vs Difference
        labels = [
            ("A3", "Total Users"),           # Row 3: Raw user counts
            ("A4", "First @ 25,000 Users"),  # Row 4: First tier pricing (up to 25K @ $0.25)
            ("A5", "25000 + Users"),         # Row 5: Second tier pricing (above 25K @ $0.24)
            ("A6", "Total Cost"),            # Row 6: Final cost calculations
            ("B2", "Telgoo"),                # Column headers
            ("C2", "MVNOC"),
            ("D2", "Difference")
        ]

        # Write all labels to their designated cells
        for cell, text in labels:
            ws.write(cell, text)

        # Write actual user count data with error handling
        try:
            # Row 3: Write the raw user counts for comparison
            ws.write("B3", telgoo_billable)  # Telgoo total users
            ws.write("C3", mvnoc_billable)   # MVNOC total users
            logger.info("Total Users added successfully")
        except Exception as e:
            # Log error but continue processing - partial reports are better than no reports
            logger.exception("Error writing total Users into row3: %s", e)

        # Write pricing formulas with comprehensive error handling
        try:
            # ===== TIERED PRICING CALCULATIONS =====

            # Row 4: First tier pricing (up to 25,000 users @ $0.25 each)
            # Formula logic: If total users > 25,000, charge for all users at $0.25
            #                Otherwise, charge $0 (handled by second tier)
            ws.write_formula("B4", "=IF(B3>25000, B3*0.25, 0)")  # Telgoo first tier
            ws.write_formula("C4", "=IF(C3>25000, C3*0.25, 0)")  # MVNOC first tier

            # Row 5: Second tier pricing (users above 25,000 @ $0.24 each)
            # Formula logic: If total users > 25,000, charge excess at $0.24
            #                Otherwise, charge $0
            ws.write_formula("B5", "=IF(B3>25000, (B3-25000)*0.24, 0)")  # Telgoo second tier
            ws.write_formula("C5", "=IF(C3>25000, (C3-25000)*0.24, 0)")  # MVNOC second tier

            # Row 6: Total cost calculation (sum of both tiers)
            ws.write_formula("B6", "=B4+B5")  # Telgoo total cost
            ws.write_formula("C6", "=C4+C5")  # MVNOC total cost

            # ===== DIFFERENCE CALCULATIONS =====
            # Calculate differences between Telgoo and MVNOC for all metrics
            ws.write_formula("D3", "=B3-C3")  # User count difference
            ws.write_formula("D4", "=B4-C4")  # First tier cost difference
            ws.write_formula("D5", "=B5-C5")  # Second tier cost difference
            ws.write_formula("D6", "=B6-C6")  # Total cost difference

            # ===== DATA SHEET CREATION =====
            # Create second sheet with complete subscriber-level data
            MSISDNWISE.to_excel(writer, sheet_name="MSISDNWISE", index=False)

            logger.info("Written summary and data sheets successfully")
        except Exception as e:
            # Log error but don't fail completely - partial reports are valuable
            logger.exception("Error writing summary and data sheets: %s", e)
    logger.info(f"Excel file saved to {file_path}")
    return file_path

def make_iot_results_excel(MSISDNWISE: pd.DataFrame, mvnoc_billable: int,telgoo_billable: int, month_name: str, year: str, logger):
    """
    Creates an Excel file with two sheets:
    1. Summary sheet: cells formatted and formulas applied as specified.
    2. Data sheet: contents of the provided DataFrame.
    
    Parameters:
    - df: DataFrame to write to 'data' sheet.
    - output_dir: Directory where the Excel file will be saved.
    - telgoo_total: Total Users count for Telgoo.
    - mvnoc_total: Total Users count for MVNOC.
    - filename: Name of the resulting Excel file.
    
    Returns the full path to the created Excel file.
    """

    filename = f"Telgoo_reconciliation_IoT_{month_name}{year}.xlsx"
    output_dir = os.getenv("REPORT_DIR")

    if output_dir is None:
        logger.error("REPORT_DIR environment variable is not set")

    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    file_path = os.path.join(output_dir, filename)
    
    # Use XlsxWriter engine for formula support
    with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
        # 1. Summary sheet
        wb  = writer.book
        ws = wb.add_worksheet("summary sheet")
        writer.sheets["summary sheet"] = ws
        
        # Write labels
        labels = [("A4", "Total Active days"),
                  ("A5", "Cost"),
                  ("B3", "Telgoo"),
                  ("C3", "MVNOC"),
                  ("D3", "Difference")]
        for cell, text in labels:
            ws.write(cell, text)
        try:
            # Write totals into row3
            ws.write("B4", telgoo_billable )
            ws.write("C4", mvnoc_billable)
            logger.info("Toatl Users added succesfully")
        except Exception as e:
            logger.exception("Error writing total Users into row3: %s", e)
        
        try:
            # Formulas for row4 (0.25 factor)
            ws.write_formula("B5", f"=B4*0.00806")
            ws.write_formula("C5", f"=C4*0.00806")
            
            # Difference columns
            ws.write_formula("D4", "=B4-C4")
            ws.write_formula("D5", "=B5-C5")
            
            # 2. Data sheet
            MSISDNWISE.to_excel(writer, sheet_name="MSISDNWISE", index=False)
            
            logger.info("Written summary and data sheets")
        except Exception as e:
            logger.exception("Error writing summary and data sheets: %s", e)
    logger.info(f"Excel file saved to {file_path}")
    return file_path

# ============================================================================
# MAIN EXECUTION FUNCTION
# ============================================================================

def main():
    """
    Main execution function for the Telgoo Reconciliation process.

    This function orchestrates the complete reconciliation workflow:
    1. Initialize logging and generate unique job ID
    2. Calculate processing date windows
    3. Load Telgoo CSV data files
    4. Connect to StarRocks and retrieve MVNOC data
    5. Perform IoT and Non-IoT reconciliation
    6. Generate Excel reports with cost calculations
    7. Clean up resources and log completion

    The function implements comprehensive error handling to ensure
    partial processing can complete even if individual steps fail.
    """

    # ===== INITIALIZATION AND LOGGING SETUP =====
    # Generate unique job ID for tracking this specific run
    run_id = generate_job_id()
    logger, handler = setup_structlog(unique_run_id=run_id, buffered=True)

    # Log job start time for performance monitoring
    try:
        start_time = datetime.today()
        logger.info("Telgoo Recon job started at %s.", start_time.strftime('%Y-%m-%d %H:%M:%S'))
    except Exception as e:
        logger.exception("Error logging start time: %s", e)

    # ===== DATE CALCULATIONS =====
    # Calculate various date references for processing and file naming
    try:
        today = datetime.today().date()
        today_usage_date = today - timedelta(days=1)    # Yesterday's usage data
        today_file_date = today_usage_date + timedelta(days=1)  # Today's file date
        logger.info("Dates computed: Today=%s, Usage Date=%s, File Date=%s",
                   today, today_usage_date, today_file_date)
    except Exception as e:
        logger.exception("Error computing dates: %s", e)

    # ===== BILLING PERIOD CALCULATION =====
    # Calculate the billing period window for data processing
    try:
        # Get the standard billing period (2nd of previous month to 1st of current month)
        #start_date, end_date = compute_window()

        # Alternative manual date setting (commented out for production)
        start_date = datetime(2025, 5, 2).strftime('%Y-%m-%d')
        end_date = datetime(2025, 6, 1).strftime('%Y-%m-%d')

        # Extract month and year information for file naming
        report_date = datetime.strptime(start_date, "%Y-%m-%d")
        month_name = report_date.strftime("%B").upper()  # Full month name in uppercase
        month_name = month_name[:3]                      # Truncate to 3 letters (e.g., 'JUN')
        year = report_date.strftime("%Y")                # Four-digit year

        # Log the calculated date range for verification
        logger.info("Dates computed: Start Date=%s, End Date=%s", start_date, end_date)
    except Exception as e:
        logger.exception("Error computing dates: %s", e)
        # Set default values if date calculation fails
        month_name = "UNK"
        year = "2025"


    '''
    #SFTP connection
    try:
        hostname = os.getenv('SFTP_HOSTNAME')
        port     = int(os.getenv('SFTP_PORT'))
        username = os.getenv('SFTP_USERNAME')
        password = os.getenv('SFTP_PASSWORD')
        logger.info("SFTP credentials loaded successfully.")

    except Exception as e:
        logger.exception("Error loading SFTP credentials: %s", e)
    try:
        transport = paramiko.Transport((hostname, port))
        transport.connect(username=username, password=password)

        # Create an SFTP session
        sftp = paramiko.SFTPClient.from_transport(transport)
        logger.info("SFTP connection established successfully.")
    except Exception as e:
        logger.exception("Error connecting to SFTP: %s", e)
    try:
        # sftp = ssh.open_sftp()
        logger.info("SFTP file transfer client created successfully.")
    except Exception as e:
        logger.exception("Error creating SFTP file transfer client: %s", e)
    try:
        dir_path = os.getenv('SFTP_DIRECTORY')
        sftp.chdir(dir_path)
        logger.info("Changed directory to %s", dir_path)
    except Exception as e:
        logger.exception("Error changing directory: %s", e)
    try:
        remote_file = f'MVNO_Connect_BIlling_{month_name}{year}.zip'
        local_file  = f'ZIP/{remote_file}'
        sftp.get(remote_file, local_file)
        logger.info("Zip File downloaded successfully.")
    except Exception as e:
        logger.exception("Error the downloading Zip file: %s", e)
    try:
        with zipfile.ZipFile(local_file, 'r') as zip_ref:
            zip_ref.extractall('ZIP/')
        logger.info("Zip file extracted successfully.")
    except Exception as e:
        logger.exception("Error extracting Zip file: %s", e)
    try:
        sftp.close()
        # ssh.close()
        logger.info("SFTP connection closed successfully.")
    except Exception as e:
        logger.exception("Error closing SFTP connection: %s", e)
    '''
 # 1) Load data
    try:
        telgoo_iot_file = os.getenv("TELGOO_IOT_CSV_FILE")
        #+ str(month_name)+ str(year) +".csv"
        telgoo_non_iot_file = os.getenv("TELGOO_NON_IOT_CSV_FILE")
        #+ str(month_name)+ str(year) +".csv"
        telgoo_iot = pd.read_csv(telgoo_iot_file, dtype={'SUBSCRIBER_ID': str})
        telgoo_non_iot = pd.read_csv(telgoo_non_iot_file, dtype={'SUBSCRIBER_ID': str})
        logger.info("Loaded data: MVNO (%s) and Telgoo (%s)", telgoo_iot, telgoo_non_iot)
    except Exception as e:
        logger.exception("Error loading input files: %s", e)
        # Initialize empty DataFrames if file loading fails
        telgoo_iot = pd.DataFrame()
        telgoo_non_iot = pd.DataFrame()

    try:
        # Set up StarRocks connection using environment variables
        # StarRocks uses MySQL protocol for connectivity
        connection = pymysql.connect(
            host=os.getenv('STARROCKS_HOST'),
            port=int(os.getenv('STARROCKS_PORT', '9030')),  # Default StarRocks query port
            user=os.getenv('STARROCKS_USER'),
            password=os.getenv('STARROCKS_PASSWORD'),
            database=os.getenv('STARROCKS_DATABASE'),
            charset='utf8mb4',
            autocommit=True
        )
        if connection:
            logger.info("Successfully connected to StarRocks.")
            cursor = connection.cursor()
        else:
            logger.error("StarRocks connection was not established.")
    except Exception as e:
        logger.exception("Error connecting to StarRocks: %s", e)
    
    # --- Retrieve IOT data ---
    try:
        # Primary method: Use stored procedure for security and performance
        # Stored procedures provide better security by preventing SQL injection
        # and encapsulating business logic within the database
        logger.info("Attempting to use stored procedure: iot_activedays_retrieve")
        try:
            query = 'CALL iot_activedays_retrieve(%s,%s)'
            cursor.execute(query, (start_date, end_date))
            cols = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()

            if not data:
                logger.warning("No IOT Snapshot data found for dates %s to %s", start_date, end_date)
                # Create empty DataFrame with all expected columns
                IOT_SNAPSHOT_BILLABLE = pd.DataFrame(columns=[
                    'SUBSCRIBER_ID', 'SUBSCRIBER_STATE', 'ACTIVE_DATE', 'DEACTIVATED_DATE',
                    'BILLING_CODE', 'WPS', 'FILE_DATE', 'TENANT_ID', 'ACTIVE_DAYS'
                ])
            else:
                IOT_SNAPSHOT_BILLABLE = pd.DataFrame(data, columns=cols)
                logger.info("✅ Stored procedure successful - Retrieved IOT Snapshot data for dates %s to %s. Total rows: %d", start_date, end_date, len(IOT_SNAPSHOT_BILLABLE))

        except Exception as sp_error:
            # Fallback method: Use direct SQL query if stored procedure fails
            logger.warning("❌ Stored procedure failed: %s", sp_error)
            logger.info("🔄 Falling back to direct SQL query for IoT data...")

            query = """
            SELECT
                PRIMARY_RESOURCE as SUBSCRIBER_ID,
                SUBSCRIBER_STATE,
                ACTIVE_DATE,
                DEACTIVATED_DATE,
                BILLING_CODE,
                WPS,
                FILE_DATE,
                TENANT_ID,
                DATEDIFF(%s, %s) + 1 as ACTIVE_DAYS
            FROM MVNOC_SNAPSHOT
            WHERE FILE_DATE >= %s
            AND FILE_DATE <= %s
            AND SUBSCRIBER_STATE = 'ACTIVE'
            AND TENANT_ID IN ('7', '1010', '1024', '1027', '4')
            """

            cursor.execute(query, (end_date, start_date, start_date, end_date))
            cols = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()

            if not data:
                logger.warning("No IOT Snapshot data found for dates %s to %s", start_date, end_date)
                IOT_SNAPSHOT_BILLABLE = pd.DataFrame(columns=[
                    'SUBSCRIBER_ID', 'SUBSCRIBER_STATE', 'ACTIVE_DATE', 'DEACTIVATED_DATE',
                    'BILLING_CODE', 'WPS', 'FILE_DATE', 'TENANT_ID', 'ACTIVE_DAYS'
                ])
            else:
                IOT_SNAPSHOT_BILLABLE = pd.DataFrame(data, columns=cols)
                logger.info("✅ Fallback SQL successful - Retrieved IOT Snapshot data for dates %s to %s. Total rows: %d", start_date, end_date, len(IOT_SNAPSHOT_BILLABLE))

    except Exception as e:
        logger.exception("Error retrieving IOT Snapshot data for dates %s to %s: %s", start_date, end_date, e)
        # Initialize empty DataFrame if both methods fail
        IOT_SNAPSHOT_BILLABLE = pd.DataFrame(columns=[
            'SUBSCRIBER_ID', 'SUBSCRIBER_STATE', 'ACTIVE_DATE', 'DEACTIVATED_DATE',
            'BILLING_CODE', 'WPS', 'FILE_DATE', 'TENANT_ID', 'ACTIVE_DAYS'
        ])
    
    # --- Retrieve NON-IOT data  ---
    try:
        # Primary method: Use stored procedure for security and performance
        # Stored procedures provide better security by preventing SQL injection
        # and encapsulating business logic within the database
        logger.info("Attempting to use stored procedure: noniot_activedays_retrieve")
        try:
            query = 'CALL noniot_activedays_retrieve(%s,%s)'
            cursor.execute(query, (start_date, end_date))
            cols = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()

            if not data:
                logger.warning("No NON-IOT Snapshot data found for dates %s to %s", start_date, end_date)
                # Create empty DataFrame with all expected columns
                non_iot_MSISDN = pd.DataFrame(columns=[
                    'SUBSCRIBER_ID', 'SUBSCRIBER_STATE', 'ACTIVE_DATE', 'DEACTIVATED_DATE',
                    'BILLING_CODE', 'WPS', 'FILE_DATE', 'TENANT_ID', 'ACTIVE_DAYS'
                ])
            else:
                non_iot_MSISDN = pd.DataFrame(data, columns=cols)
                logger.info("✅ Stored procedure successful - Retrieved NON-IOT Snapshot data for dates %s to %s. Total rows: %d", start_date, end_date, len(non_iot_MSISDN))

        except Exception as sp_error:
            # Fallback method: Use direct SQL query if stored procedure fails
            logger.warning("❌ Stored procedure failed: %s", sp_error)
            logger.info("🔄 Falling back to direct SQL query for Non-IoT data...")

            query = """
            SELECT
                PRIMARY_RESOURCE as SUBSCRIBER_ID,
                SUBSCRIBER_STATE,
                ACTIVE_DATE,
                DEACTIVATED_DATE,
                BILLING_CODE,
                WPS,
                FILE_DATE,
                TENANT_ID,
                DATEDIFF(%s, %s) + 1 as ACTIVE_DAYS
            FROM MVNOC_SNAPSHOT
            WHERE FILE_DATE >= %s
            AND FILE_DATE <= %s
            AND SUBSCRIBER_STATE = 'ACTIVE'
            AND TENANT_ID IN ('1006', '1016', '1004', '1036', '1008', '1025')
            """

            cursor.execute(query, (end_date, start_date, start_date, end_date))
            cols = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()

            if not data:
                logger.warning("No NON-IOT Snapshot data found for dates %s to %s", start_date, end_date)
                non_iot_MSISDN = pd.DataFrame(columns=[
                    'SUBSCRIBER_ID', 'SUBSCRIBER_STATE', 'ACTIVE_DATE', 'DEACTIVATED_DATE',
                    'BILLING_CODE', 'WPS', 'FILE_DATE', 'TENANT_ID', 'ACTIVE_DAYS'
                ])
            else:
                non_iot_MSISDN = pd.DataFrame(data, columns=cols)
                logger.info("✅ Fallback SQL successful - Retrieved NON-IOT Snapshot data for dates %s to %s. Total rows: %d", start_date, end_date, len(non_iot_MSISDN))

    except Exception as e:
        logger.exception("Error retrieving NON-IOT Snapshot data for dates %s to %s: %s", start_date, end_date, e)
        # Initialize empty DataFrame if both methods fail
        non_iot_MSISDN = pd.DataFrame(columns=[
            'SUBSCRIBER_ID', 'SUBSCRIBER_STATE', 'ACTIVE_DATE', 'DEACTIVATED_DATE',
            'BILLING_CODE', 'WPS', 'FILE_DATE', 'TENANT_ID', 'ACTIVE_DAYS'
        ])
        

    try:
        telgoo_noniot_users, MSISDNWISE_non_iot, mvnoc_noniot_users= non_iot_recon(non_iot_MSISDN, telgoo_non_iot, logger)
    except Exception as e:
        logger.exception("Error in Non-IoT Reconciliation': %s", e)
        telgoo_noniot_users =0 
        MSISDNWISE_non_iot = pd.DataFrame()
        mvnoc_noniot_users=0
    
    try:
        report_path = make_non_iot_results_excel(
        MSISDNWISE_non_iot,
        mvnoc_noniot_users,
        telgoo_noniot_users,
        month_name,
        year,
        logger
        )
        logger.info("Non-Iot Reconciliation made successfully.")
    except Exception as e:
        logger.exception("Error in Non-Iot Reconciliation report genration.': %s", e)

    try:
        MSISDNWISE_iot, mvnoc_billable_days, telgoo_billable_days= iot_recon(IOT_SNAPSHOT_BILLABLE, telgoo_iot, logger)
        logger.info("Iot Reconciliation made successfully.")
    except Exception as e:
        logger.exception("Error in Iot Reconciliation': %s", e)
        MSISDNWISE_iot= pd.DataFrame()
        mvnoc_billable_days=0
        telgoo_billable_days=0

    try:
        report_path = make_iot_results_excel(
        MSISDNWISE_iot,
        mvnoc_billable_days,
        telgoo_billable_days,
        month_name,
        year,
        logger
        )
        logger.info("Iot Reconciliation report generated successfully.")
    except Exception as e:
        logger.exception("Error in Iot Reconciliation report genration.': %s", e)


    try:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
        logger.info("StarRocks connection is closed.")
    except Exception as close_ex:
        logger.exception("Error while closing StarRocks connection: %s", close_ex)

    try:
        end_time = datetime.today()
        logger.info("Telgoo reconciliation job ended at %s.", end_time.strftime('%Y-%m-%d %H:%M:%S'))
    except Exception as e:
        logger.exception("Error logging end time: %s", e)
        
        handler.flush()

if __name__ == "__main__":
    main()
