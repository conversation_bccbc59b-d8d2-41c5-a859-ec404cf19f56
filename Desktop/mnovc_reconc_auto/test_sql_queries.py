#!/usr/bin/env python3
"""
Test the new SQL queries that replace the Snowflake stored procedures.
"""

import pymysql
import pandas as pd
import os
from dotenv import load_dotenv
from datetime import datetime, timedelta

def test_sql_queries():
    """Test the new SQL queries for IoT and Non-IoT data retrieval."""
    
    load_dotenv()
    
    host = os.getenv('STARROCKS_HOST')
    port = int(os.getenv('STARROCKS_PORT', '9030'))
    user = os.getenv('STARROCKS_USER')
    password = os.getenv('STARROCKS_PASSWORD')
    database = os.getenv('STARROCKS_DATABASE')
    
    print("🧪 Testing StarRocks SQL Queries")
    print("=" * 50)
    
    try:
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4',
            autocommit=True
        )
        cursor = connection.cursor()
        
        # Test date range (last month)
        end_date = datetime.now().replace(day=1).date()
        start_date = (end_date - timedelta(days=1)).replace(day=2)
        
        print(f"📅 Testing with date range: {start_date} to {end_date}")
        
        # 1. First, let's understand the data better
        print("\n1️⃣ Data Analysis:")
        
        # Check unique BILLING_CODE values
        cursor.execute("SELECT DISTINCT BILLING_CODE FROM MVNOC_SNAPSHOT WHERE BILLING_CODE IS NOT NULL LIMIT 20")
        billing_codes = cursor.fetchall()
        print(f"   BILLING_CODE values: {[code[0] for code in billing_codes]}")
        
        # Check unique WPS values
        cursor.execute("SELECT DISTINCT WPS FROM MVNOC_SNAPSHOT WHERE WPS IS NOT NULL LIMIT 20")
        wps_values = cursor.fetchall()
        print(f"   WPS values: {[wps[0] for wps in wps_values]}")
        
        # Check date range in data
        cursor.execute("SELECT MIN(FILE_DATE), MAX(FILE_DATE) FROM MVNOC_SNAPSHOT")
        date_range = cursor.fetchone()
        print(f"   Data date range: {date_range[0]} to {date_range[1]}")
        
        # 2. Test IoT Query
        print(f"\n2️⃣ Testing IoT Query:")
        
        iot_query = """
        SELECT 
            PRIMARY_RESOURCE as SUBSCRIBER_ID,
            SUBSCRIBER_STATE,
            ACTIVE_DATE,
            DEACTIVATED_DATE,
            BILLING_CODE,
            WPS,
            FILE_DATE,
            DATEDIFF(%s, %s) + 1 as ACTIVE_DAYS
        FROM MVNOC_SNAPSHOT 
        WHERE FILE_DATE >= %s 
        AND FILE_DATE <= %s
        AND SUBSCRIBER_STATE = 'ACTIVE'
        AND (BILLING_CODE IN ('IOT', 'M2M') OR WPS LIKE '%%IOT%%')
        LIMIT 10
        """
        
        try:
            cursor.execute(iot_query, (end_date, start_date, start_date, end_date))
            iot_data = cursor.fetchall()
            cols = [desc[0] for desc in cursor.description]
            
            print(f"   ✅ IoT query executed successfully")
            print(f"   📊 Sample results: {len(iot_data)} rows")
            
            if iot_data:
                iot_df = pd.DataFrame(iot_data, columns=cols)
                print(f"   Columns: {list(iot_df.columns)}")
                print(f"   Sample data:")
                print(iot_df.head(3).to_string(index=False))
            else:
                print("   ⚠️  No IoT data found with current criteria")
                
        except Exception as e:
            print(f"   ❌ IoT query failed: {e}")
        
        # 3. Test Non-IoT Query
        print(f"\n3️⃣ Testing Non-IoT Query:")
        
        noniot_query = """
        SELECT 
            PRIMARY_RESOURCE as SUBSCRIBER_ID,
            SUBSCRIBER_STATE,
            ACTIVE_DATE,
            DEACTIVATED_DATE,
            BILLING_CODE,
            WPS,
            FILE_DATE,
            DATEDIFF(%s, %s) + 1 as ACTIVE_DAYS
        FROM MVNOC_SNAPSHOT 
        WHERE FILE_DATE >= %s 
        AND FILE_DATE <= %s
        AND SUBSCRIBER_STATE = 'ACTIVE'
        AND (BILLING_CODE NOT IN ('IOT', 'M2M') AND WPS NOT LIKE '%%IOT%%')
        LIMIT 10
        """
        
        try:
            cursor.execute(noniot_query, (end_date, start_date, start_date, end_date))
            noniot_data = cursor.fetchall()
            cols = [desc[0] for desc in cursor.description]
            
            print(f"   ✅ Non-IoT query executed successfully")
            print(f"   📊 Sample results: {len(noniot_data)} rows")
            
            if noniot_data:
                noniot_df = pd.DataFrame(noniot_data, columns=cols)
                print(f"   Columns: {list(noniot_df.columns)}")
                print(f"   Sample data:")
                print(noniot_df.head(3).to_string(index=False))
            else:
                print("   ⚠️  No Non-IoT data found with current criteria")
                
        except Exception as e:
            print(f"   ❌ Non-IoT query failed: {e}")
        
        # 4. Test with broader criteria if no data found
        print(f"\n4️⃣ Testing with broader criteria:")
        
        # Test without IoT/Non-IoT filtering
        broad_query = """
        SELECT 
            PRIMARY_RESOURCE as SUBSCRIBER_ID,
            SUBSCRIBER_STATE,
            BILLING_CODE,
            WPS,
            COUNT(*) as COUNT
        FROM MVNOC_SNAPSHOT 
        WHERE FILE_DATE >= %s 
        AND FILE_DATE <= %s
        AND SUBSCRIBER_STATE = 'ACTIVE'
        GROUP BY PRIMARY_RESOURCE, SUBSCRIBER_STATE, BILLING_CODE, WPS
        LIMIT 10
        """
        
        try:
            cursor.execute(broad_query, (start_date, end_date))
            broad_data = cursor.fetchall()
            cols = [desc[0] for desc in cursor.description]
            
            print(f"   ✅ Broad query executed successfully")
            print(f"   📊 Results: {len(broad_data)} unique combinations")
            
            if broad_data:
                broad_df = pd.DataFrame(broad_data, columns=cols)
                print(f"   Sample data:")
                print(broad_df.head(5).to_string(index=False))
                
                # Analyze the data to suggest better IoT/Non-IoT criteria
                unique_billing = broad_df['BILLING_CODE'].unique()
                unique_wps = broad_df['WPS'].unique()
                
                print(f"\n   💡 Suggestions for IoT/Non-IoT identification:")
                print(f"   BILLING_CODE values in data: {unique_billing}")
                print(f"   WPS values in data: {unique_wps}")
                
        except Exception as e:
            print(f"   ❌ Broad query failed: {e}")
        
        cursor.close()
        connection.close()
        
        print(f"\n📋 Summary:")
        print(f"   • StarRocks connection: ✅ Working")
        print(f"   • SQL queries: ✅ Syntax correct")
        print(f"   • Data availability: Check results above")
        print(f"\n💡 Next steps:")
        print(f"   1. Adjust IoT/Non-IoT identification criteria based on actual data")
        print(f"   2. Test with the actual Telgoo reconciliation process")
        print(f"   3. Verify date ranges match your billing periods")
        
    except Exception as e:
        print(f"❌ Error testing queries: {e}")

if __name__ == "__main__":
    test_sql_queries()
