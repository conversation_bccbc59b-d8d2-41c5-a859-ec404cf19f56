# Telgoo Reconciliation Automation

## Overview

The Telgoo Reconciliation Automation system is a Python-based application designed to reconcile Telgoo invoices and generate comprehensive reconciliation reports for both IoT and Non-IoT users. This system automates the billing reconciliation process between MVNOC's internal data and Telgoo's billing system.

## Features

- **Automated Data Reconciliation**: Compares MVNOC billing data with Telgoo invoice data
- **IoT and Non-IoT Processing**: Separate reconciliation workflows for IoT and Non-IoT subscribers
- **Excel Report Generation**: Creates detailed Excel reports with summary sheets and raw data
- **Snowflake Integration**: Retrieves active subscriber data from Snowflake database
- **SFTP Support**: Downloads billing files from remote SFTP servers
- **Structured Logging**: Comprehensive JSON-based logging with unique job IDs
- **Enhanced Error Handling**: Multi-layered exception handling with granular error isolation
  - Individual formula writing error handling
  - Separate error handling for data operations
  - Graceful degradation with partial report generation
- **Robust Report Generation**: Continues processing even when individual Excel operations fail

## Project Structure

```
mnovc_reconc_auto/
├── src/
│   ├── main.py                    # Main application entry point
│   ├── struct_logger.py           # Custom structured logging implementation
├── final_report/                  # Generated Excel reports
├── logs/                          # Application logs (organized by date)
├── ZIP/                           # Downloaded and extracted files
├── *.csv                          # Input CSV files (Telgoo billing data)
├── .env                           # Environment configuration
└── README.md                      # This file
```

## Requirements

### Python Dependencies

- pandas >= 1.3.0
- numpy >= 1.21.0
- snowflake-connector-python >= 3.0.0
- paramiko >= 2.7.0
- boto3 >= 1.20.0
- python-dotenv >= 0.19.0
- structlog >= 21.0.0
- xlsxwriter >= 3.0.0
- python-dateutil >= 2.8.0

### System Requirements

- Python 3.8 or higher
- Access to Snowflake database
- Network access to SFTP server (for telgoo_reconciliation.py)
- Sufficient disk space for CSV files and reports

## Installation

1. **Clone or download the project**:
   ```bash
   cd Desktop/mnovc_reconc_auto
   ```

2. **Create a virtual environment**:
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install pandas numpy snowflake-connector-python paramiko boto3 python-dotenv structlog xlsxwriter python-dateutil
   ```

4. **Configure environment variables** (see Configuration section)

## Configuration

Create a `.env` file in the project root with the following variables:

```env
# CSV File Paths
TELGOO_IOT_CSV_FILE=/path/to/MVNO_Connect_BIlling_IOT_JUNE2025.csv
TELGOO_NON_IOT_CSV_FILE=/path/to/MVNO_Connect_BIlling_JUNE2025.csv

# Output Directories
REPORT_DIR=/path/to/final_report
LOGGER_DIR=/path/to/logs

# Snowflake Configuration
SNOWFLAKE_USER=your_username
SNOWFLAKE_PASSWORD=your_password
SNOWFLAKE_ACCOUNT=your_account
SNOWFLAKE_WAREHOUSE=your_warehouse
SNOWFLAKE_DATABASE=your_database
SNOWFLAKE_SCHEMA=your_schema
SNOWFLAKE_ROLE=your_role

# AWS Configuration (if using S3)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_BUCKET_NAME=your_bucket_name

# SFTP Configuration
SFTP_HOSTNAME=your_sftp_host
SFTP_PORT=22
SFTP_USERNAME=your_username
SFTP_PASSWORD=your_password
SFTP_DIRECTORY=/path/to/remote/directory
```

## Usage

### Running the Main Application

```bash
cd src
python main.py
```

### Running with SFTP Download

```bash
cd src
python telgoo_reconciliation.py
```

### Input Files

The application expects CSV files with the following structure:

**IoT CSV File**:
- TENANT_ID
- SUBSCRIBER_ID
- PRIMARY_RESOURCE
- SUBSCRIBER_STATE
- STATE_VALID_FROM
- DEACTIVATED_DATE
- NAME
- NO_OF_ACTIVE_DAYS
- BILLABLE_DAYS

**Non-IoT CSV File**:
- TENANT_ID
- SUBSCRIBER_ID
- PRIMARY_RESOURCE
- SUBSCRIBER_STATE
- STATE_VALID_FROM
- DEACTIVATED_DATE
- NAME
- NO_OF_ACTIVE_DAYS

## Output

### Excel Reports

The application generates two Excel reports:

1. **Telgoo_reconciliation_IoT_[MONTH][YEAR].xlsx**
   - Summary sheet with active days and cost calculations
   - MSISDNWISE sheet with detailed subscriber data

2. **Telgoo_reconciliation_NonIoT_[MONTH][YEAR].xlsx**
   - Summary sheet with user counts and tiered pricing
   - MSISDNWISE sheet with detailed subscriber data

### Logs

Structured JSON logs are created in the `logs/` directory, organized by date:
- Format: `logs/YYYY-MM-DD/application_YYYYMMDD_HHMMSS.json`
- Each log entry includes timestamp, level, source file, message, and job ID

## Key Functions

### Data Processing

- `iot_recon()`: Processes IoT subscriber reconciliation
- `non_iot_recon()`: Processes Non-IoT subscriber reconciliation
- `compute_window()`: Calculates date ranges for data retrieval

### Report Generation

- `make_iot_results_excel()`: Creates IoT reconciliation Excel report with robust error handling
- `make_non_iot_results_excel()`: Creates Non-IoT reconciliation Excel report with robust error handling
  - Both functions feature granular exception handling for formula writing and data operations
  - Separate error handling for summary sheet formulas and data sheet creation
  - Continues processing even if individual operations fail

### Utilities

- `setup_structlog()`: Configures structured logging
- `generate_job_id()`: Creates unique job identifiers

## Troubleshooting

### Common Issues

1. **File Not Found Errors**: Ensure CSV file paths in `.env` are correct
2. **Snowflake Connection Issues**: Verify credentials and network connectivity
3. **Permission Errors**: Check file system permissions for output directories
4. **Memory Issues**: Large CSV files may require increased system memory
5. **Excel Generation Errors**: The application now handles individual Excel operation failures gracefully
   - Formula writing errors are isolated and logged separately
   - Data sheet creation continues even if summary formulas fail
   - Partial reports are generated when possible

### Log Analysis

Check the latest log file in `logs/[current-date]/` for detailed error information. Each log entry includes:
- Timestamp
- Log level (INFO, ERROR, etc.)
- Source file name
- Detailed error messages and stack traces
- Unique job ID for tracking

## Version History

- **Version 1.1** (July 2025) - Enhanced error handling
  - Improved Excel report generation with granular exception handling
  - Isolated formula writing errors for better resilience
  - Enhanced data sheet creation with separate error handling
  - Better error recovery for partial report generation

- **Version 1.0** (June 25, 2025) - Initial version by Vyomakesh
  - Basic reconciliation functionality
  - Excel report generation
  - Snowflake integration
  - Structured logging

## Data Flow

1. **Date Calculation**: Automatically computes processing window (2nd of previous month to 1st of current month)
2. **Data Retrieval**:
   - Loads Telgoo CSV files (IoT and Non-IoT)
   - Retrieves MVNOC snapshot data from Snowflake
3. **Reconciliation**:
   - Merges Telgoo and MVNOC data by SUBSCRIBER_ID
   - Calculates billable days/users for comparison
4. **Report Generation**: Creates Excel files with summary and detailed data
5. **Logging**: Records all operations with structured JSON logging

## Pricing Models

### IoT Pricing
- Cost per active day: $0.00806
- Formula: `Total Active Days × $0.00806`

### Non-IoT Pricing (Tiered)
- First 25,000 users: $0.25 per user
- Additional users (25,000+): $0.24 per user
- Formula: `MIN(users, 25000) × $0.25 + MAX(0, users - 25000) × $0.24`

## Database Schema

### Snowflake Stored Procedures
- `iot_activedays_retrieve(start_date, end_date)`: Retrieves IoT active days data
- `noniot_activedays_retrieve(start_date, end_date)`: Retrieves Non-IoT active days data

### Expected Columns
- **SUBSCRIBER_ID**: Unique subscriber identifier
- **ACTIVE_DAYS**: Number of active days in the period
- **SUBSCRIBER_STATE**: Current state (ACTIVE/DEACTIVATED)
- **TENANT_ID**: Tenant identifier for filtering

## Error Handling

The application implements comprehensive error handling with granular exception management:

- **File Loading**: Graceful handling of missing CSV files with empty DataFrame fallbacks
- **Database Connections**: Automatic retry and fallback mechanisms for Snowflake connectivity
- **Data Processing**: Validation and error recovery for malformed data with detailed logging
- **Report Generation**: Multi-level error handling including:
  - Directory creation failures with fallback paths
  - Excel formula writing errors (isolated per formula)
  - Data sheet creation errors (separate from summary sheet)
  - Individual cell writing errors with continuation of processing
- **Logging Integration**: Each error is captured with full stack traces and context

## Performance Considerations

- **Memory Usage**: Large CSV files (300K+ rows) may require 4GB+ RAM
- **Processing Time**: Typical run time is 2-5 minutes depending on data size
- **Disk Space**: Ensure sufficient space for logs, reports, and temporary files

## Security Notes

- Store sensitive credentials in `.env` file (never commit to version control)
- Use environment-specific configuration files
- Regularly rotate database and SFTP passwords
- Monitor log files for sensitive data exposure

## Development

### Adding New Features

1. Follow the existing error handling patterns
2. Add comprehensive logging for new operations
3. Update this README with new configuration options
4. Test with sample data before production deployment

### Testing

Run the test files in the `src/` directory:
```bash
python test_exception_handling.py
python test_with_output.py
```

## Support

For issues or questions:
1. Check the latest log file in `logs/[current-date]/` for detailed error information
2. Verify environment configuration in `.env` file
3. Ensure all dependencies are installed and up to date
4. The structured logging system provides comprehensive debugging information for troubleshooting
