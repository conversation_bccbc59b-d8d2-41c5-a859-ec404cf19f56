# Snowflake to StarRocks Migration Guide

## Overview

This document outlines the migration from Snowflake to StarRocks as the data warehouse for the Telgoo Reconciliation System.

## Changes Made

### 1. Database Connector
- **Before**: `snowflake-connector-python`
- **After**: `pymysql` (StarRocks uses MySQL protocol)

### 2. Connection Parameters
- **Before**: Snowflake-specific parameters (account, warehouse, role, schema)
- **After**: Standard MySQL parameters (host, port, database)

### 3. Environment Variables

#### Removed Variables:
```
SNOWFLAKE_USER
SNOWFLAKE_PASSWORD
SNOWFLAKE_ACCOUNT
SNOWFLAKE_WAREHOUSE
SNOWFLAKE_DATABASE
SNOWFLAKE_SCHEMA
SNOWFLAKE_ROLE
```

#### New Variables:
```
STARROCKS_HOST=your_starrocks_host
STARROCKS_PORT=9030
STARROCKS_USER=your_username
STARROCKS_PASSWORD=your_password
STARROCKS_DATABASE=your_database
```

### 4. Stored Procedure Syntax
- **Before**: `CALL "procedure_name"(param1, param2)`
- **After**: `CALL procedure_name(param1, param2)`

### 5. Connection Code Changes

#### Before (Snowflake):
```python
import snowflake.connector

connection = snowflake.connector.connect(
    user=os.getenv('SNOWFLAKE_USER'),
    password=os.getenv('SNOWFLAKE_PASSWORD'),
    account=os.getenv('SNOWFLAKE_ACCOUNT'),
    warehouse=os.getenv('SNOWFLAKE_WAREHOUSE'),
    database=os.getenv('SNOWFLAKE_DATABASE'),
    schema=os.getenv('SNOWFLAKE_SCHEMA'),
    role=os.getenv('SNOWFLAKE_ROLE')
)
```

#### After (StarRocks):
```python
import pymysql

connection = pymysql.connect(
    host=os.getenv('STARROCKS_HOST'),
    port=int(os.getenv('STARROCKS_PORT', '9030')),
    user=os.getenv('STARROCKS_USER'),
    password=os.getenv('STARROCKS_PASSWORD'),
    database=os.getenv('STARROCKS_DATABASE'),
    charset='utf8mb4',
    autocommit=True
)
```

## Migration Steps

### 1. Update Dependencies
```bash
# Uninstall Snowflake connector
pip uninstall snowflake-connector-python

# Install PyMySQL for StarRocks
pip install pymysql>=1.0.0

# Or install all dependencies
pip install -r requirements.txt
```

### 2. Update Environment Variables
Update your `.env` file with the new StarRocks configuration:

```env
# Replace Snowflake variables with StarRocks variables
STARROCKS_HOST=your_starrocks_host
STARROCKS_PORT=9030
STARROCKS_USER=your_username
STARROCKS_PASSWORD=your_password
STARROCKS_DATABASE=your_database
```

### 3. Verify Stored Procedures
Ensure the following stored procedures exist in StarRocks:
- `iot_activedays_retrieve(start_date, end_date)`
- `noniot_activedays_retrieve(start_date, end_date)`

### 4. Test Connection
Run a simple connection test:

```python
import pymysql
import os
from dotenv import load_dotenv

load_dotenv()

try:
    connection = pymysql.connect(
        host=os.getenv('STARROCKS_HOST'),
        port=int(os.getenv('STARROCKS_PORT', '9030')),
        user=os.getenv('STARROCKS_USER'),
        password=os.getenv('STARROCKS_PASSWORD'),
        database=os.getenv('STARROCKS_DATABASE'),
        charset='utf8mb4',
        autocommit=True
    )
    print("StarRocks connection successful!")
    connection.close()
except Exception as e:
    print(f"Connection failed: {e}")
```

## Compatibility Notes

### StarRocks vs Snowflake Differences

1. **Query Port**: StarRocks typically uses port 9030 for queries
2. **Protocol**: StarRocks uses MySQL protocol, not proprietary Snowflake protocol
3. **Stored Procedures**: Syntax is MySQL-compatible
4. **Data Types**: May need verification for compatibility
5. **Connection Pooling**: PyMySQL handles connection management differently

### Potential Issues

1. **Data Type Mapping**: Verify that data types from StarRocks match expected pandas DataFrame types
2. **Stored Procedure Results**: Ensure stored procedures return data in the same format
3. **Connection Timeout**: May need to adjust timeout settings for large queries
4. **Character Encoding**: UTF-8 encoding is explicitly set for StarRocks

## Testing Checklist

- [ ] StarRocks connection establishes successfully
- [ ] IoT stored procedure returns expected data structure
- [ ] Non-IoT stored procedure returns expected data structure
- [ ] Data reconciliation produces same results as Snowflake
- [ ] Excel reports generate correctly
- [ ] Error handling works as expected
- [ ] Performance is acceptable for typical data volumes

## Rollback Plan

If issues arise, you can rollback by:

1. Reverting the code changes
2. Reinstalling snowflake-connector-python
3. Restoring original environment variables
4. Using the previous version of the application

## Support

For issues with this migration:
1. Check StarRocks connectivity and credentials
2. Verify stored procedures exist and return expected data
3. Review logs for specific error messages
4. Test with smaller date ranges first
