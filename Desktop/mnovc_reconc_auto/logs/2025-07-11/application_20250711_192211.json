{"asctime": "2025-07-11 19:22:11,259463", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "Telgoo Recon job started at 2025-07-11 19:22:11.", "job_id": "20250711_192211"}
{"asctime": "2025-07-11 19:23:27,482224", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "Successfully connected to Snow<PERSON><PERSON>.", "job_id": "20250711_192211"}
{"asctime": "2025-07-11 19:23:32,397034", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "Retrieved IOT Snapshot data for dates 2025-06-02 to 2025-07-01. Total rows: 345634  ", "job_id": "20250711_192211"}
{"asctime": "2025-07-11 19:23:36,190348", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "Retrieved NON-IOT Snapshot data for dates 2025-06-02 to 2025-07-01. Total rows: 348266  ", "job_id": "20250711_192211"}
{"asctime": "2025-07-11 19:23:36,190443", "levelname": "ERROR", "name": "telgoo_reconciliation.py", "message": "Error in Non-IoT Reconciliation': cannot access local variable 'telgoo_non_iot' where it is not associated with a value", "job_id": "20250711_192211", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 408, in main\n    telgoo_noniot_users, MSISDNWISE_non_iot, mvnoc_noniot_users= non_iot_recon(non_iot_MSISDN, telgoo_non_iot)\n                                                                                               ^^^^^^^^^^^^^^\nUnboundLocalError: cannot access local variable 'telgoo_non_iot' where it is not associated with a value"}
{"asctime": "2025-07-11 19:23:36,210998", "levelname": "ERROR", "name": "telgoo_reconciliation.py", "message": "Error in Non-Iot Reconciliation report genration.': 'NoneType' object has no attribute 'exception'", "job_id": "20250711_192211", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 168, in make_non_iot_results_excel\n    logger.info(\"Toatl Users added succesfully\")\n    ^^^^^^^^^^^\nAttributeError: 'NoneType' object has no attribute 'info'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 416, in main\n    report_path = make_non_iot_results_excel(\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 170, in make_non_iot_results_excel\n    logger.exception(\"Error writing total Users into row3: %s\", e)\n    ^^^^^^^^^^^^^^^^\nAttributeError: 'NoneType' object has no attribute 'exception'"}
{"asctime": "2025-07-11 19:23:36,211668", "levelname": "ERROR", "name": "telgoo_reconciliation.py", "message": "Error in Iot Reconciliation': cannot access local variable 'telgoo_iot' where it is not associated with a value", "job_id": "20250711_192211", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 428, in main\n    MSISDNWISE_iot, mvnoc_billable_days, telgoo_billable_days= iot_recon(IOT_SNAPSHOT_BILLABLE, telgoo_iot)\n                                                                                                ^^^^^^^^^^\nUnboundLocalError: cannot access local variable 'telgoo_iot' where it is not associated with a value"}
{"asctime": "2025-07-11 19:23:36,212050", "levelname": "ERROR", "name": "telgoo_reconciliation.py", "message": "Error in Iot Reconciliation report genration.': 'NoneType' object has no attribute 'error'", "job_id": "20250711_192211", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 437, in main\n    report_path = make_iot_results_excel(\n                  ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconciliation.py\", line 218, in make_iot_results_excel\n    logger.error(\"REPORT_DIR environment variable is not set\")\n    ^^^^^^^^^^^^\nAttributeError: 'NoneType' object has no attribute 'error'"}
{"asctime": "2025-07-11 19:23:36,855532", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "Snowflake connection is closed.", "job_id": "20250711_192211"}
{"asctime": "2025-07-11 19:23:36,855783", "levelname": "INFO", "name": "telgoo_reconciliation.py", "message": "Telgoo reconciliation job ended at 2025-07-11 19:23:36.", "job_id": "20250711_192211"}
