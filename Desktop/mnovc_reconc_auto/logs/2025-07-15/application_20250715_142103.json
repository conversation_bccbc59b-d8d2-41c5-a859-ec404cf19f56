{"asctime": "2025-07-15 14:21:03,397696", "levelname": "INFO", "name": "main.py", "message": "Telgoo Recon job started at 2025-07-15 14:21:03.", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:03,397787", "levelname": "INFO", "name": "main.py", "message": "Dates computed: Today=2025-07-15, Usage Date=2025-07-14, File Date=2025-07-15", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:03,398013", "levelname": "INFO", "name": "main.py", "message": "Dates computed: Start Date=2025-06-02, End Date=2025-07-01", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:04,058446", "levelname": "INFO", "name": "main.py", "message": "Loaded data: MVNO (        TENANT_ID     SUBSCRIBER_ID  PRIMARY_RESOURCE SUBSCRIBER_STATE  ...     DEACTIVATED_DATE        NAME NO_OF_ACTIVE_DAYS  BILLABLE_DAYS\n0               8  2251799813764194        5623848598      DEACTIVATED  ...  2025-06-04 08:31:13     Megatel               608              4\n1            1010  2251799813882176        2104127314           ACTIVE  ...                   \\N  scriptsave               596             30\n2            1010  2251799813882179        2104429874           ACTIVE  ...                   \\N  scriptsave               596             30\n3            1010  2251799813882268        2105191917           ACTIVE  ...                   \\N  scriptsave               596             30\n4            1010  2251799813882275        2105357443           ACTIVE  ...                   \\N  scriptsave               596             30\n...           ...               ...               ...              ...  ...                  ...         ...               ...            ...\n342018       1009  2251799817581998        6466967035           ACTIVE  ...                   \\N        boom                 2              1\n342019       1009  2251799817582005        6466910723           ACTIVE  ...                   \\N        boom                 2              1\n342020       1009  2251799817582015        6466967705           ACTIVE  ...                   \\N        boom                 2              1\n342021       1009  2251799817582045        9294945100           ACTIVE  ...                   \\N        boom                 2              1\n342022       1009  2251799817582049        2674232546           ACTIVE  ...                   \\N        boom                 2              1\n\n[342023 rows x 9 columns]) and Telgoo (        TENANT_ID     SUBSCRIBER_ID  PRIMARY_RESOURCE SUBSCRIBER_STATE     STATE_VALID_FROM     DEACTIVATED_DATE                 NAME  NO_OF_ACTIVE_DAYS\n0               7                91        4045782582           ACTIVE  2023-04-06 20:51:42                   \\N     MVNOConnect Demo                817\n1               8  2251799813685375        6892766522      DEACTIVATED  2023-06-09 11:05:12  2025-06-02 13:55:53              Megatel                724\n2               7  2251799813685394        6892805700           ACTIVE  2023-06-12 18:35:06                   \\N     MVNOConnect Demo                750\n3            1000  2251799813685416        9403442101      DEACTIVATED  2023-06-14 12:45:03  2025-06-30 07:03:55           US Connect                747\n4            1000  2251799813685417        9403442102      DEACTIVATED  2023-06-14 12:50:05  2025-06-30 07:04:01           US Connect                747\n...           ...               ...               ...              ...                  ...                  ...                  ...                ...\n348057       1016  2251799817581975        4424739162           ACTIVE  2025-06-30 23:02:00                   \\N  expertcommunication                  2\n348058       1006  2251799817582027        8483869135           ACTIVE  2025-06-30 23:21:17                   \\N             USMobile                  2\n348059       1006  2251799817582040        4044526626           ACTIVE  2025-06-30 23:36:23                   \\N             USMobile                  2\n348060       1006  2251799817582042        9297644684           ACTIVE  2025-06-30 23:43:53                   \\N             USMobile                  2\n348061       1006  2251799817582044        4693808961           ACTIVE  2025-06-30 23:44:20                   \\N             USMobile                  2\n\n[348062 rows x 8 columns])", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:53,238639", "levelname": "INFO", "name": "snowflake.connector.connection", "message": "Snowflake Connector for Python Version: 3.15.0, Python Version: 3.12.7, Platform: macOS-15.5-arm64-arm-64bit", "job_id": null}
{"asctime": "2025-07-15 14:21:53,238700", "levelname": "INFO", "name": "snowflake.connector.connection", "message": "Connecting to GLOBAL Snowflake domain", "job_id": null}
{"asctime": "2025-07-15 14:21:05,208270", "levelname": "INFO", "name": "main.py", "message": "Successfully connected to Snowflake.", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:09,193885", "levelname": "INFO", "name": "main.py", "message": "Retrieved IOT Snapshot data for dates 2025-06-02 to 2025-07-01. Total rows: 345634  ", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:14,806871", "levelname": "INFO", "name": "main.py", "message": "Retrieved NON-IOT Snapshot data for dates 2025-06-02 to 2025-07-01. Total rows: 348266  ", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:14,807069", "levelname": "INFO", "name": "main.py", "message": "Calculated Telgoo non-IoT users: 348062", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:14,939970", "levelname": "INFO", "name": "main.py", "message": "Toatl Users added succesfully", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:26,420152", "levelname": "INFO", "name": "main.py", "message": "Written summary and data sheets", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:32,634269", "levelname": "INFO", "name": "main.py", "message": "Excel file saved to /Users/<USER>/Desktop/mnovc_reconc_auto/final_report/Telgoo_reconciliation_NonIoT_JUN2025.xlsx", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:32,634516", "levelname": "INFO", "name": "main.py", "message": "Non-Iot Reconciliation made successfully.", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:32,635872", "levelname": "INFO", "name": "main.py", "message": "Filtered Telgoo IoT billable records: 5446212 rows", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:32,716602", "levelname": "INFO", "name": "main.py", "message": "Filtered snapdhot IoT billable records: 5332577 rows", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:32,716691", "levelname": "INFO", "name": "main.py", "message": "Iot Reconciliation made successfully.", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:32,719602", "levelname": "INFO", "name": "main.py", "message": "Toatl Users added succesfully", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:45,882242", "levelname": "INFO", "name": "main.py", "message": "Written summary and data sheets", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:52,549909", "levelname": "INFO", "name": "main.py", "message": "Excel file saved to /Users/<USER>/Desktop/mnovc_reconc_auto/final_report/Telgoo_reconciliation_IoT_JUN2025.xlsx", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:52,550167", "levelname": "INFO", "name": "main.py", "message": "Iot Reconciliation report generated successfully.", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:53,167980", "levelname": "INFO", "name": "main.py", "message": "Snowflake connection is closed.", "job_id": "20250715_142103"}
{"asctime": "2025-07-15 14:21:53,168134", "levelname": "INFO", "name": "main.py", "message": "Telgoo reconciliation job ended at 2025-07-15 14:21:53.", "job_id": "20250715_142103"}
