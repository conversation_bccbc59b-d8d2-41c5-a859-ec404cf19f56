{"asctime": "2025-10-08 16:08:27,555603", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Telgoo Recon job started at 2025-10-08 16:08:27.", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:27,555674", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Dates computed: Today=2025-10-08, Usage Date=2025-10-07, File Date=2025-10-08", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:27,555878", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Dates computed: Start Date=2025-09-02, End Date=2025-10-01", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:28,077235", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Loaded data: MVNO (        TENANT_ID     SUBSCRIBER_ID  PRIMARY_RESOURCE SUBSCRIBER_STATE  ... DEACTIVATED_DATE        NAME NO_OF_ACTIVE_DAYS  BILLABLE_DAYS\n0            1010  2251799813871053        5612709465           ACTIVE  ...               \\N  scriptsave               631             31\n1            1010  2251799813882186        2104458655           ACTIVE  ...               \\N  scriptsave               627             31\n2            1010  2251799813882263        2104738422           ACTIVE  ...               \\N  scriptsave               627             31\n3            1010  2251799813882266        2104739829           ACTIVE  ...               \\N  scriptsave               627             31\n4            1010  2251799813882273        2105352132           ACTIVE  ...               \\N  scriptsave               627             31\n...           ...               ...               ...              ...  ...              ...         ...               ...            ...\n174475       1009  2251799817663371        9012463692           ACTIVE  ...               \\N        boom                 2              1\n174476       1009  2251799817663375        9174179198           ACTIVE  ...               \\N        boom                 2              1\n174477       1009  2251799817663383        3473611465           ACTIVE  ...               \\N        boom                 2              1\n174478       1009  2251799817663389        3513451736           ACTIVE  ...               \\N        boom                 2              1\n174479       1009  2251799817663407        9174179506           ACTIVE  ...               \\N        boom                 2              1\n\n[174480 rows x 9 columns]) and Telgoo (        TENANT_ID     SUBSCRIBER_ID  PRIMARY_RESOURCE  ... DEACTIVATED_DATE                 NAME NO_OF_ACTIVE_DAYS\n0               7  2251799813685790        6892808081  ...               \\N     MVNOConnect Demo               742\n1               7  2251799813685829        6892808252  ...               \\N     MVNOConnect Demo               738\n2               7  2251799813685830        6892808253  ...               \\N     MVNOConnect Demo               738\n3               7  2251799813688393        6892963035  ...               \\N     MVNOConnect Demo               703\n4               7  2251799813688394        6892963044  ...               \\N     MVNOConnect Demo               703\n...           ...               ...               ...  ...              ...                  ...               ...\n334181       1016  2251799817663286        2135512232  ...               \\N  expertcommunication                 2\n334182       1006  2251799817663307        9297120300  ...               \\N             USMobile                 2\n334183       1006  2251799817663315        6466109149  ...               \\N             USMobile                 2\n334184       1006  2251799817663319        7866455612  ...               \\N             USMobile                 2\n334185       1006  2251799817663411        6692909888  ...               \\N             USMobile                 2\n\n[334186 rows x 8 columns])", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:30,982336", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Successfully connected to StarRocks.", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:31,633062", "levelname": "WARNING", "name": "telgoo_reconiliation.py", "message": "No IOT Snapshot data found for dates 2025-09-02 to 2025-10-01", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:32,289304", "levelname": "WARNING", "name": "telgoo_reconiliation.py", "message": "No NON-IOT Snapshot data found for dates 2025-09-02 to 2025-10-01", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:32,291895", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Calculated Telgoo non-IoT users: 334186", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:32,385864", "levelname": "ERROR", "name": "telgoo_reconiliation.py", "message": "Error filtering snapshot non-IoT data: 'SUBSCRIBER_STATE'", "job_id": "20251008_160827", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/core/indexes/base.py\", line 3805, in get_loc\n    return self._engine.get_loc(casted_key)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"index.pyx\", line 167, in pandas._libs.index.IndexEngine.get_loc\n  File \"index.pyx\", line 196, in pandas._libs.index.IndexEngine.get_loc\n  File \"pandas/_libs/hashtable_class_helper.pxi\", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item\n  File \"pandas/_libs/hashtable_class_helper.pxi\", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item\nKeyError: 'SUBSCRIBER_STATE'\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/telgoo_reconiliation.py\", line 174, in non_iot_recon\n    valid_records = ~((MSISDNWISE_non_iot['SUBSCRIBER_STATE'] == 'ACTIVE') &\n                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/core/frame.py\", line 4102, in __getitem__\n    indexer = self.columns.get_loc(key)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/core/indexes/base.py\", line 3812, in get_loc\n    raise KeyError(key) from err\nKeyError: 'SUBSCRIBER_STATE'"}
{"asctime": "2025-10-08 16:08:32,420700", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Total Users added successfully", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:32,422537", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Written summary and data sheets successfully", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:32,427708", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Excel file saved to /Users/<USER>/Desktop/mnovc_reconc_auto/final_report/Telgoo_reconciliation_NonIoT_SEP2025.xlsx", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:32,427771", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Non-Iot Reconciliation made successfully.", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:32,428192", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Filtered Telgoo IoT billable records: 3404892 rows", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:32,464762", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Filtered snapshot IoT billable records: 0 rows", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:32,464849", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Iot Reconciliation made successfully.", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:32,465292", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Toatl Users added succesfully", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:40,263966", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Written summary and data sheets", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:43,952532", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Excel file saved to /Users/<USER>/Desktop/mnovc_reconc_auto/final_report/Telgoo_reconciliation_IoT_SEP2025.xlsx", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:43,953043", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Iot Reconciliation report generated successfully.", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:43,953462", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "StarRocks connection is closed.", "job_id": "20251008_160827"}
{"asctime": "2025-10-08 16:08:43,953551", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Telgoo reconciliation job ended at 2025-10-08 16:08:43.", "job_id": "20251008_160827"}
