{"asctime": "2025-07-15 13:55:58,536521", "levelname": "INFO", "name": "main.py", "message": "Telgoo Recon job started at 2025-07-15 13:55:58.", "job_id": "20250715_135558"}
{"asctime": "2025-07-15 13:55:58,536616", "levelname": "INFO", "name": "main.py", "message": "Dates computed: Today=2025-07-15, Usage Date=2025-07-14, File Date=2025-07-15", "job_id": "20250715_135558"}
{"asctime": "2025-07-15 13:55:58,536858", "levelname": "INFO", "name": "main.py", "message": "Dates computed: Start Date=2025-06-02, End Date=2025-07-01", "job_id": "20250715_135558"}
{"asctime": "2025-07-15 13:55:58,538096", "levelname": "ERROR", "name": "main.py", "message": "Error loading input files: [Errno 2] No such file or directory: '/Users/<USER>/Desktop/mnovc_reconc_auto/MVNO_Connect_BIlling_IOT_'", "job_id": "20250715_135558", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 353, in main\n    telgoo_iot = pd.read_csv(telgoo_iot_file, dtype={'SUBSCRIBER_ID': str})\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py\", line 1026, in read_csv\n    return _read(filepath_or_buffer, kwds)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py\", line 620, in _read\n    parser = TextFileReader(filepath_or_buffer, **kwds)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py\", line 1620, in __init__\n    self._engine = self._make_engine(f, self.engine)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py\", line 1880, in _make_engine\n    self.handles = get_handle(\n                   ^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/common.py\", line 873, in get_handle\n    handle = open(\n             ^^^^^\nFileNotFoundError: [Errno 2] No such file or directory: '/Users/<USER>/Desktop/mnovc_reconc_auto/MVNO_Connect_BIlling_IOT_'"}
{"asctime": "2025-07-15 13:56:09,012144", "levelname": "INFO", "name": "snowflake.connector.connection", "message": "Snowflake Connector for Python Version: 3.15.0, Python Version: 3.12.7, Platform: macOS-15.5-arm64-arm-64bit", "job_id": null}
{"asctime": "2025-07-15 13:56:09,012225", "levelname": "INFO", "name": "snowflake.connector.connection", "message": "Connecting to GLOBAL Snowflake domain", "job_id": null}
{"asctime": "2025-07-15 13:55:59,868747", "levelname": "INFO", "name": "main.py", "message": "Successfully connected to Snowflake.", "job_id": "20250715_135558"}
{"asctime": "2025-07-15 13:56:05,134638", "levelname": "INFO", "name": "main.py", "message": "Retrieved IOT Snapshot data for dates 2025-06-02 to 2025-07-01. Total rows: 345634  ", "job_id": "20250715_135558"}
{"asctime": "2025-07-15 13:56:09,011055", "levelname": "INFO", "name": "main.py", "message": "Retrieved NON-IOT Snapshot data for dates 2025-06-02 to 2025-07-01. Total rows: 348266  ", "job_id": "20250715_135558"}
{"asctime": "2025-07-15 13:56:09,011826", "levelname": "ERROR", "name": "main.py", "message": "Error in Non-IoT Reconciliation': cannot access local variable 'telgoo_non_iot' where it is not associated with a value", "job_id": "20250715_135558", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 410, in main\n    telgoo_noniot_users, MSISDNWISE_non_iot, mvnoc_noniot_users= non_iot_recon(non_iot_MSISDN, telgoo_non_iot)\n                                                                                               ^^^^^^^^^^^^^^\nUnboundLocalError: cannot access local variable 'telgoo_non_iot' where it is not associated with a value"}
{"asctime": "2025-07-15 13:56:09,033222", "levelname": "ERROR", "name": "main.py", "message": "Error in Non-Iot Reconciliation report genration.': 'NoneType' object has no attribute 'exception'", "job_id": "20250715_135558", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 168, in make_non_iot_results_excel\n    logger.info(\"Toatl Users added succesfully\")\n    ^^^^^^^^^^^\nAttributeError: 'NoneType' object has no attribute 'info'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 418, in main\n    report_path = make_non_iot_results_excel(\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 170, in make_non_iot_results_excel\n    logger.exception(\"Error writing total Users into row3: %s\", e)\n    ^^^^^^^^^^^^^^^^\nAttributeError: 'NoneType' object has no attribute 'exception'"}
{"asctime": "2025-07-15 13:56:09,033874", "levelname": "ERROR", "name": "main.py", "message": "Error in Iot Reconciliation': cannot access local variable 'telgoo_iot' where it is not associated with a value", "job_id": "20250715_135558", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 430, in main\n    MSISDNWISE_iot, mvnoc_billable_days, telgoo_billable_days= iot_recon(IOT_SNAPSHOT_BILLABLE, telgoo_iot)\n                                                                                                ^^^^^^^^^^\nUnboundLocalError: cannot access local variable 'telgoo_iot' where it is not associated with a value"}
{"asctime": "2025-07-15 13:56:09,037272", "levelname": "ERROR", "name": "main.py", "message": "Error in Iot Reconciliation report genration.': 'NoneType' object has no attribute 'exception'", "job_id": "20250715_135558", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 243, in make_iot_results_excel\n    logger.info(\"Toatl Users added succesfully\")\n    ^^^^^^^^^^^\nAttributeError: 'NoneType' object has no attribute 'info'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 439, in main\n    report_path = make_iot_results_excel(\n                  ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 245, in make_iot_results_excel\n    logger.exception(\"Error writing total Users into row3: %s\", e)\n    ^^^^^^^^^^^^^^^^\nAttributeError: 'NoneType' object has no attribute 'exception'"}
{"asctime": "2025-07-15 13:56:09,612113", "levelname": "INFO", "name": "main.py", "message": "Snowflake connection is closed.", "job_id": "20250715_135558"}
{"asctime": "2025-07-15 13:56:09,612317", "levelname": "INFO", "name": "main.py", "message": "Telgoo reconciliation job ended at 2025-07-15 13:56:09.", "job_id": "20250715_135558"}
