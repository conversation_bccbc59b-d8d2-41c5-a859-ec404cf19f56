# StarRocks Stored Procedure Compatibility Guide

## Overview
This document explains how to modify the code to support both stored procedures (like Snowflake) and direct SQL queries in StarRocks.

## Current Situation
- **StarRocks Version 8.0.33**: Does NOT support `CALL` syntax for stored procedures
- **Current Implementation**: Uses direct SQL queries with TENANT_ID filtering
- **Potential Future**: StarRocks may add stored procedure support

## Flexible Implementation Strategy

### 1. Environment Variable Configuration
Add a configuration option to choose between stored procedures and direct queries:

```env
# Add to .env file
STARROCKS_USE_STORED_PROCEDURES=false  # Set to true if stored procedures are available
```

### 2. Adaptive Query Function
Create a function that tries stored procedures first, then falls back to direct SQL:

```python
def get_iot_data(cursor, start_date, end_date, use_stored_procedures=False):
    """
    Retrieve IoT data using either stored procedures or direct SQL queries.
    
    Args:
        cursor: Database cursor
        start_date: Start date for data retrieval
        end_date: End date for data retrieval
        use_stored_procedures: Boolean flag to attempt stored procedure first
    
    Returns:
        tuple: (data, columns) or (None, None) if both methods fail
    """
    
    if use_stored_procedures:
        # Method 1: Try stored procedure first
        try:
            logger.info("Attempting to use stored procedure: iot_activedays_retrieve")
            query = 'CALL iot_activedays_retrieve(%s,%s)'
            cursor.execute(query, (start_date, end_date))
            cols = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
            logger.info("✅ Stored procedure successful: %d rows retrieved", len(data))
            return data, cols
            
        except Exception as e:
            logger.warning("❌ Stored procedure failed: %s", e)
            logger.info("🔄 Falling back to direct SQL query...")
    
    # Method 2: Direct SQL query (fallback or primary method)
    try:
        logger.info("Using direct SQL query for IoT data")
        query = """
        SELECT 
            PRIMARY_RESOURCE as SUBSCRIBER_ID,
            SUBSCRIBER_STATE,
            ACTIVE_DATE,
            DEACTIVATED_DATE,
            BILLING_CODE,
            WPS,
            FILE_DATE,
            TENANT_ID,
            DATEDIFF(%s, %s) + 1 as ACTIVE_DAYS
        FROM MVNOC_SNAPSHOT 
        WHERE FILE_DATE >= %s 
        AND FILE_DATE <= %s
        AND SUBSCRIBER_STATE = 'ACTIVE'
        AND TENANT_ID IN ('7', '1010', '1024', '1027', '4')
        """
        
        cursor.execute(query, (end_date, start_date, start_date, end_date))
        cols = [desc[0] for desc in cursor.description]
        data = cursor.fetchall()
        logger.info("✅ Direct SQL successful: %d rows retrieved", len(data))
        return data, cols
        
    except Exception as e:
        logger.exception("❌ Both stored procedure and direct SQL failed: %s", e)
        return None, None

def get_noniot_data(cursor, start_date, end_date, use_stored_procedures=False):
    """
    Retrieve Non-IoT data using either stored procedures or direct SQL queries.
    """
    
    if use_stored_procedures:
        # Method 1: Try stored procedure first
        try:
            logger.info("Attempting to use stored procedure: noniot_activedays_retrieve")
            query = 'CALL noniot_activedays_retrieve(%s,%s)'
            cursor.execute(query, (start_date, end_date))
            cols = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
            logger.info("✅ Stored procedure successful: %d rows retrieved", len(data))
            return data, cols
            
        except Exception as e:
            logger.warning("❌ Stored procedure failed: %s", e)
            logger.info("🔄 Falling back to direct SQL query...")
    
    # Method 2: Direct SQL query (fallback or primary method)
    try:
        logger.info("Using direct SQL query for Non-IoT data")
        query = """
        SELECT 
            PRIMARY_RESOURCE as SUBSCRIBER_ID,
            SUBSCRIBER_STATE,
            ACTIVE_DATE,
            DEACTIVATED_DATE,
            BILLING_CODE,
            WPS,
            FILE_DATE,
            TENANT_ID,
            DATEDIFF(%s, %s) + 1 as ACTIVE_DAYS
        FROM MVNOC_SNAPSHOT 
        WHERE FILE_DATE >= %s 
        AND FILE_DATE <= %s
        AND SUBSCRIBER_STATE = 'ACTIVE'
        AND TENANT_ID IN ('1006', '1016', '1004', '1036', '1008', '1025')
        """
        
        cursor.execute(query, (end_date, start_date, start_date, end_date))
        cols = [desc[0] for desc in cursor.description]
        data = cursor.fetchall()
        logger.info("✅ Direct SQL successful: %d rows retrieved", len(data))
        return data, cols
        
    except Exception as e:
        logger.exception("❌ Both stored procedure and direct SQL failed: %s", e)
        return None, None
```

### 3. Updated Main Code Integration

```python
# In the main function, replace the current data retrieval sections:

# --- Retrieve IOT data ---
try:
    use_stored_procedures = os.getenv('STARROCKS_USE_STORED_PROCEDURES', 'false').lower() == 'true'
    data, cols = get_iot_data(cursor, start_date, end_date, use_stored_procedures)
    
    if data is not None:
        IOT_SNAPSHOT_BILLABLE = pd.DataFrame(data, columns=cols)
        logger.info("Retrieved IOT Snapshot data for dates %s to %s. Total rows: %d", 
                   start_date, end_date, len(IOT_SNAPSHOT_BILLABLE))
    else:
        logger.warning("No IOT Snapshot data found for dates %s to %s", start_date, end_date)
        IOT_SNAPSHOT_BILLABLE = pd.DataFrame(columns=[
            'SUBSCRIBER_ID', 'SUBSCRIBER_STATE', 'ACTIVE_DATE', 'DEACTIVATED_DATE', 
            'BILLING_CODE', 'WPS', 'FILE_DATE', 'TENANT_ID', 'ACTIVE_DAYS'
        ])
        
except Exception as e:
    logger.exception("Error retrieving IOT Snapshot data: %s", e)
    IOT_SNAPSHOT_BILLABLE = pd.DataFrame(columns=[
        'SUBSCRIBER_ID', 'SUBSCRIBER_STATE', 'ACTIVE_DATE', 'DEACTIVATED_DATE', 
        'BILLING_CODE', 'WPS', 'FILE_DATE', 'TENANT_ID', 'ACTIVE_DAYS'
    ])

# --- Retrieve NON-IOT data ---
try:
    use_stored_procedures = os.getenv('STARROCKS_USE_STORED_PROCEDURES', 'false').lower() == 'true'
    data, cols = get_noniot_data(cursor, start_date, end_date, use_stored_procedures)
    
    if data is not None:
        non_iot_MSISDN = pd.DataFrame(data, columns=cols)
        logger.info("Retrieved NON-IOT Snapshot data for dates %s to %s. Total rows: %d", 
                   start_date, end_date, len(non_iot_MSISDN))
    else:
        logger.warning("No NON-IOT Snapshot data found for dates %s to %s", start_date, end_date)
        non_iot_MSISDN = pd.DataFrame(columns=[
            'SUBSCRIBER_ID', 'SUBSCRIBER_STATE', 'ACTIVE_DATE', 'DEACTIVATED_DATE', 
            'BILLING_CODE', 'WPS', 'FILE_DATE', 'TENANT_ID', 'ACTIVE_DAYS'
        ])
        
except Exception as e:
    logger.exception("Error retrieving NON-IOT Snapshot data: %s", e)
    non_iot_MSISDN = pd.DataFrame(columns=[
        'SUBSCRIBER_ID', 'SUBSCRIBER_STATE', 'ACTIVE_DATE', 'DEACTIVATED_DATE', 
        'BILLING_CODE', 'WPS', 'FILE_DATE', 'TENANT_ID', 'ACTIVE_DAYS'
    ])
```

## Usage Scenarios

### Scenario 1: StarRocks with Stored Procedures
```env
STARROCKS_USE_STORED_PROCEDURES=true
```
- Application tries stored procedures first
- Falls back to direct SQL if procedures fail
- Logs which method was successful

### Scenario 2: StarRocks without Stored Procedures (Current)
```env
STARROCKS_USE_STORED_PROCEDURES=false
```
- Application uses direct SQL queries only
- Faster execution (no failed procedure attempts)
- Current working configuration

### Scenario 3: Auto-Detection
```python
def detect_stored_procedure_support(cursor):
    """
    Automatically detect if StarRocks supports stored procedures.
    """
    try:
        # Try to list stored procedures
        cursor.execute("SHOW PROCEDURE STATUS")
        return True
    except:
        try:
            # Alternative detection method
            cursor.execute("SELECT ROUTINE_NAME FROM information_schema.ROUTINES LIMIT 1")
            return True
        except:
            return False

# In main function:
stored_proc_support = detect_stored_procedure_support(cursor)
logger.info("Stored procedure support detected: %s", stored_proc_support)
```

## Benefits of This Approach

1. **Backward Compatibility**: Works with both old Snowflake-style stored procedures and new direct SQL
2. **Forward Compatibility**: Ready for future StarRocks versions that might support stored procedures
3. **Graceful Degradation**: Falls back to working method if preferred method fails
4. **Performance Optimization**: Can use faster method when available
5. **Easy Configuration**: Simple environment variable to control behavior
6. **Comprehensive Logging**: Clear visibility into which method is being used

## Migration Path

1. **Current State**: Direct SQL queries working
2. **If Stored Procedures Become Available**: 
   - Create the stored procedures in StarRocks
   - Set `STARROCKS_USE_STORED_PROCEDURES=true`
   - Application automatically uses stored procedures with SQL fallback
3. **Testing**: Can easily switch between methods for performance comparison

This approach ensures your application will work regardless of StarRocks' stored procedure capabilities!
