#!/usr/bin/env python3
"""
StarRocks Database Explorer
Explores the StarRocks database to understand the schema and available objects.
"""

import pymysql
import os
from dotenv import load_dotenv

def explore_starrocks():
    """Explore StarRocks database structure."""
    
    load_dotenv()
    
    host = os.getenv('STARROCKS_HOST')
    port = int(os.getenv('STARROCKS_PORT', '9030'))
    user = os.getenv('STARROCKS_USER')
    password = os.getenv('STARROCKS_PASSWORD')
    database = os.getenv('STARROCKS_DATABASE')
    
    print("🔍 Exploring StarRocks Database Structure")
    print("=" * 50)
    
    try:
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4',
            autocommit=True
        )
        cursor = connection.cursor()
        
        # 1. Show all databases
        print("1️⃣ Available Databases:")
        cursor.execute("SHOW DATABASES")
        databases = cursor.fetchall()
        for db in databases:
            marker = " ← CURRENT" if db[0] == database else ""
            print(f"   📁 {db[0]}{marker}")
        
        # 2. Show tables in current database
        print(f"\n2️⃣ Tables in '{database}' database:")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        if tables:
            for table in tables:
                print(f"   📋 {table[0]}")
        else:
            print("   ❌ No tables found")
        
        # 3. Check for functions/procedures (StarRocks specific)
        print(f"\n3️⃣ Functions and Procedures:")
        try:
            # Try different ways to list functions
            cursor.execute("SHOW FUNCTIONS")
            functions = cursor.fetchall()
            if functions:
                for func in functions:
                    print(f"   🔧 {func}")
            else:
                print("   ❌ No functions found with SHOW FUNCTIONS")
        except Exception as e:
            print(f"   ⚠️  SHOW FUNCTIONS not supported: {e}")
        
        # 4. Try to find stored procedures in information_schema
        print(f"\n4️⃣ Checking Information Schema:")
        try:
            cursor.execute("""
                SELECT ROUTINE_NAME, ROUTINE_TYPE, ROUTINE_SCHEMA 
                FROM information_schema.ROUTINES 
                WHERE ROUTINE_SCHEMA = %s
            """, (database,))
            routines = cursor.fetchall()
            if routines:
                for routine in routines:
                    print(f"   🔧 {routine[1]}: {routine[0]} (schema: {routine[2]})")
            else:
                print("   ❌ No routines found in information_schema")
        except Exception as e:
            print(f"   ⚠️  Information schema query failed: {e}")
        
        # 5. Check if we can access other databases
        print(f"\n5️⃣ Checking Other Databases for Tables:")
        for db in databases:
            db_name = db[0]
            if db_name not in ['information_schema', 'mysql', 'performance_schema', 'sys']:
                try:
                    cursor.execute(f"USE {db_name}")
                    cursor.execute("SHOW TABLES")
                    db_tables = cursor.fetchall()
                    if db_tables:
                        print(f"   📁 {db_name}: {len(db_tables)} tables")
                        for table in db_tables[:3]:  # Show first 3 tables
                            print(f"      📋 {table[0]}")
                        if len(db_tables) > 3:
                            print(f"      ... and {len(db_tables) - 3} more")
                    else:
                        print(f"   📁 {db_name}: No tables")
                except Exception as e:
                    print(f"   📁 {db_name}: Access denied or error - {e}")
        
        # 6. Try to find the specific procedures we need
        print(f"\n6️⃣ Searching for Required Procedures:")
        procedures_to_find = ['iot_activedays_retrieve', 'noniot_activedays_retrieve']
        
        for db in databases:
            db_name = db[0]
            try:
                cursor.execute(f"USE {db_name}")
                for proc_name in procedures_to_find:
                    try:
                        # Try different ways to check if procedure exists
                        cursor.execute(f"DESCRIBE {proc_name}")
                        print(f"   ✅ Found {proc_name} in {db_name}")
                    except:
                        try:
                            cursor.execute(f"SHOW CREATE PROCEDURE {proc_name}")
                            print(f"   ✅ Found procedure {proc_name} in {db_name}")
                        except:
                            pass  # Procedure doesn't exist in this database
            except:
                pass  # Can't access this database
        
        # 7. Check StarRocks version and capabilities
        print(f"\n7️⃣ StarRocks Information:")
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        print(f"   Version: {version[0]}")
        
        try:
            cursor.execute("SHOW VARIABLES LIKE 'version%'")
            variables = cursor.fetchall()
            for var in variables:
                print(f"   {var[0]}: {var[1]}")
        except:
            pass
        
        cursor.close()
        connection.close()
        
        print(f"\n📋 Summary:")
        print(f"   • Connection: ✅ Working")
        print(f"   • Database '{database}': ✅ Accessible")
        print(f"   • Tables: {'✅ Found' if tables else '❌ None found'}")
        print(f"   • Required procedures: ❓ Need to be located or created")
        
    except Exception as e:
        print(f"❌ Error exploring database: {e}")

if __name__ == "__main__":
    explore_starrocks()
