# Snowflake to StarRocks Migration Guide

## Overview

This document outlines the migration from Snowflake to StarRocks as the data warehouse for the Telgoo Reconciliation System.

## Changes Made

### 1. Database Connector
- **Before**: `snowflake-connector-python`
- **After**: `pymysql` (StarRocks uses MySQL protocol)

### 2. Connection Parameters
- **Before**: Snowflake-specific parameters (account, warehouse, role, schema)
- **After**: Standard MySQL parameters (host, port, database)

### 3. Environment Variables

#### Removed Variables:
```
SNOWFLAKE_USER
SNOWFLAKE_PASSWORD
SNOWFLAKE_ACCOUNT
SNOWFLAKE_WAREHOUSE
SNOWFLAKE_DATABASE
SNOWFLAKE_SCHEMA
SNOWFLAKE_ROLE
```

#### New Variables:
```
STARROCKS_HOST=your_starrocks_host
STARROCKS_PORT=9030
STARROCKS_USER=your_username
STARROCKS_PASSWORD=your_password
STARROCKS_DATABASE=your_database
```

### 4. Stored Procedure Syntax
- **Before**: `CALL "procedure_name"(param1, param2)`
- **After**: `CALL procedure_name(param1, param2)`

### 5. Connection Code Changes

#### Before (Snowflake):
```python
import snowflake.connector

connection = snowflake.connector.connect(
    user=os.getenv('SNOWFLAKE_USER'),
    password=os.getenv('SNOWFLAKE_PASSWORD'),
    account=os.getenv('SNOWFLAKE_ACCOUNT'),
    warehouse=os.getenv('SNOWFLAKE_WAREHOUSE'),
    database=os.getenv('SNOWFLAKE_DATABASE'),
    schema=os.getenv('SNOWFLAKE_SCHEMA'),
    role=os.getenv('SNOWFLAKE_ROLE')
)
```

#### After (StarRocks):
```python
import pymysql

connection = pymysql.connect(
    host=os.getenv('STARROCKS_HOST'),
    port=int(os.getenv('STARROCKS_PORT', '9030')),
    user=os.getenv('STARROCKS_USER'),
    password=os.getenv('STARROCKS_PASSWORD'),
    database=os.getenv('STARROCKS_DATABASE'),
    charset='utf8mb4',
    autocommit=True
)
```

## Migration Steps

### 1. Update Dependencies
```bash
# Uninstall Snowflake connector
pip uninstall snowflake-connector-python

# Install PyMySQL for StarRocks
pip install pymysql>=1.0.0

# Or install all dependencies
pip install -r requirements.txt
```

### 2. Update Environment Variables
Update your `.env` file with the new StarRocks configuration:

```env
# Replace Snowflake variables with StarRocks variables
STARROCKS_HOST="**************"
STARROCKS_PORT=9030
STARROCKS_USER="madhav"
STARROCKS_PASSWORD="madhav123"
STARROCKS_DATABASE="billing_data"
```

### 3. ⚠️ Important: No Stored Procedures Needed
**StarRocks doesn't support stored procedures like Snowflake.** Instead, the migration replaces stored procedure calls with direct SQL queries:

- ❌ `CALL iot_activedays_retrieve(start_date, end_date)`
- ✅ Direct SQL query with TENANT_ID filtering for IoT identification

### 4. Key Data Insights Discovered

**Database Structure:**
- Database: `billing_data` (not `mvnoc`)
- Main table: `MVNOC_SNAPSHOT`
- Data range: 2025-05-01 to 2025-08-31 (58M+ records)
- Daily records: ~280K active subscribers

**IoT vs Non-IoT Identification:**
- ❌ No obvious IoT indicators in BILLING_CODE or WPS
- ✅ Use TENANT_ID for classification:
  - **IoT Tenants**: 7, 1010, 1024, 1027, 4 (smaller subscriber bases)
  - **Non-IoT Tenants**: 1006, 1016, 1004, 1036, 1008, 1025 (major tenants)

**SQL Query Approach:**
```sql
-- IoT Query
SELECT PRIMARY_RESOURCE as SUBSCRIBER_ID, SUBSCRIBER_STATE, TENANT_ID,
       DATEDIFF(end_date, start_date) + 1 as ACTIVE_DAYS
FROM MVNOC_SNAPSHOT
WHERE FILE_DATE >= start_date AND FILE_DATE <= end_date
AND SUBSCRIBER_STATE = 'ACTIVE'
AND TENANT_ID IN ('7', '1010', '1024', '1027', '4')

-- Non-IoT Query
-- Same query but with TENANT_ID IN ('1006', '1016', '1004', '1036', '1008', '1025')
```

### 5. Test Connection
Run the provided test script:

```bash
python test_starrocks_connection.py
```

## Compatibility Notes

### StarRocks vs Snowflake Differences

1. **Query Port**: StarRocks typically uses port 9030 for queries
2. **Protocol**: StarRocks uses MySQL protocol, not proprietary Snowflake protocol
3. **Stored Procedures**: Syntax is MySQL-compatible
4. **Data Types**: May need verification for compatibility
5. **Connection Pooling**: PyMySQL handles connection management differently

### Potential Issues

1. **Data Type Mapping**: Verify that data types from StarRocks match expected pandas DataFrame types
2. **Stored Procedure Results**: Ensure stored procedures return data in the same format
3. **Connection Timeout**: May need to adjust timeout settings for large queries
4. **Character Encoding**: UTF-8 encoding is explicitly set for StarRocks

## Testing Checklist

- [ ] StarRocks connection establishes successfully
- [ ] IoT stored procedure returns expected data structure
- [ ] Non-IoT stored procedure returns expected data structure
- [ ] Data reconciliation produces same results as Snowflake
- [ ] Excel reports generate correctly
- [ ] Error handling works as expected
- [ ] Performance is acceptable for typical data volumes

## Rollback Plan

If issues arise, you can rollback by:

1. Reverting the code changes
2. Reinstalling snowflake-connector-python
3. Restoring original environment variables
4. Using the previous version of the application

## Support

For issues with this migration:
1. Check StarRocks connectivity and credentials
2. Verify stored procedures exist and return expected data
3. Review logs for specific error messages
4. Test with smaller date ranges first
