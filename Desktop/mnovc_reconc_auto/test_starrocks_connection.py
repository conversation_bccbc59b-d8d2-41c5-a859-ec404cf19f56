#!/usr/bin/env python3
"""
StarRocks Connection Test Script
Tests the database connection and stored procedures for the Telgoo reconciliation system.
"""

import pymysql
import os
from dotenv import load_dotenv
from datetime import datetime, timed<PERSON>ta

def test_starrocks_connection():
    """Test StarRocks database connection and stored procedures."""
    
    # Load environment variables
    load_dotenv()
    
    print("🔍 Testing StarRocks Connection...")
    print("=" * 50)
    
    # Get connection parameters
    host = os.getenv('STARROCKS_HOST')
    port = int(os.getenv('STARROCKS_PORT', '9030'))
    user = os.getenv('STARROCKS_USER')
    password = os.getenv('STARROCKS_PASSWORD')
    database = os.getenv('STARROCKS_DATABASE')
    
    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"User: {user}")
    print(f"Database: {database}")
    print("-" * 50)
    
    try:
        # Test basic connection
        print("1️⃣ Testing basic connection...")
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4',
            autocommit=True,
            connect_timeout=10
        )
        print("✅ Connection successful!")
        
        # Test cursor creation
        print("\n2️⃣ Testing cursor creation...")
        cursor = connection.cursor()
        print("✅ Cursor created successfully!")
        
        # Test basic query
        print("\n3️⃣ Testing basic query...")
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        print(f"✅ StarRocks version: {version[0] if version else 'Unknown'}")
        
        # Test database access
        print("\n4️⃣ Testing database access...")
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"✅ Found {len(tables)} tables in database")
        if tables:
            print("   Tables:", [table[0] for table in tables[:5]])  # Show first 5 tables
        
        # Test stored procedures
        print("\n5️⃣ Testing stored procedures...")
        
        # Test dates (last month)
        end_date = datetime.now().replace(day=1).date()
        start_date = (end_date - timedelta(days=1)).replace(day=2)
        
        print(f"   Using date range: {start_date} to {end_date}")
        
        # Test IoT stored procedure
        try:
            print("   Testing iot_activedays_retrieve...")
            cursor.execute("CALL iot_activedays_retrieve(%s,%s)", (start_date, end_date))
            iot_data = cursor.fetchall()
            print(f"   ✅ IoT procedure returned {len(iot_data)} rows")
            if iot_data:
                cols = [desc[0] for desc in cursor.description]
                print(f"   Columns: {cols}")
        except Exception as e:
            print(f"   ❌ IoT procedure failed: {e}")
        
        # Test Non-IoT stored procedure
        try:
            print("   Testing noniot_activedays_retrieve...")
            cursor.execute("CALL noniot_activedays_retrieve(%s,%s)", (start_date, end_date))
            noniot_data = cursor.fetchall()
            print(f"   ✅ Non-IoT procedure returned {len(noniot_data)} rows")
            if noniot_data:
                cols = [desc[0] for desc in cursor.description]
                print(f"   Columns: {cols}")
        except Exception as e:
            print(f"   ❌ Non-IoT procedure failed: {e}")
        
        # Cleanup
        cursor.close()
        connection.close()
        print("\n🎉 All tests completed successfully!")
        print("✅ StarRocks is ready for the Telgoo reconciliation system!")
        
    except pymysql.Error as e:
        print(f"\n❌ Database error: {e}")
        print("\n🔧 Troubleshooting tips:")
        print("1. Verify StarRocks server is running")
        print("2. Check network connectivity to the host")
        print("3. Verify credentials are correct")
        print("4. Ensure the database exists")
        print("5. Check if port 9030 is accessible")
        
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("\n🔧 Check your .env file configuration")

if __name__ == "__main__":
    test_starrocks_connection()
