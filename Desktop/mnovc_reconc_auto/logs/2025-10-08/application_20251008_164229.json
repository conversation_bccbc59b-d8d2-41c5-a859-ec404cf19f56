{"asctime": "2025-10-08 16:42:29,939330", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Telgoo Recon job started at 2025-10-08 16:42:29.", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:42:29,939403", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Dates computed: Today=2025-10-08, Usage Date=2025-10-07, File Date=2025-10-08", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:42:29,939584", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Dates computed: Start Date=2025-05-02, End Date=2025-06-01", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:42:30,487318", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Loaded data: MVNO (        TENANT_ID     SUBSCRIBER_ID  PRIMARY_RESOURCE SUBSCRIBER_STATE  ... DEACTIVATED_DATE        NAME NO_OF_ACTIVE_DAYS  BILLABLE_DAYS\n0            1010  2251799813871053        5612709465           ACTIVE  ...               \\N  scriptsave               631             31\n1            1010  2251799813882186        2104458655           ACTIVE  ...               \\N  scriptsave               627             31\n2            1010  2251799813882263        2104738422           ACTIVE  ...               \\N  scriptsave               627             31\n3            1010  2251799813882266        2104739829           ACTIVE  ...               \\N  scriptsave               627             31\n4            1010  2251799813882273        2105352132           ACTIVE  ...               \\N  scriptsave               627             31\n...           ...               ...               ...              ...  ...              ...         ...               ...            ...\n174475       1009  2251799817663371        9012463692           ACTIVE  ...               \\N        boom                 2              1\n174476       1009  2251799817663375        9174179198           ACTIVE  ...               \\N        boom                 2              1\n174477       1009  2251799817663383        3473611465           ACTIVE  ...               \\N        boom                 2              1\n174478       1009  2251799817663389        3513451736           ACTIVE  ...               \\N        boom                 2              1\n174479       1009  2251799817663407        9174179506           ACTIVE  ...               \\N        boom                 2              1\n\n[174480 rows x 9 columns]) and Telgoo (        TENANT_ID     SUBSCRIBER_ID  PRIMARY_RESOURCE  ... DEACTIVATED_DATE                 NAME NO_OF_ACTIVE_DAYS\n0               7  2251799813685790        6892808081  ...               \\N     MVNOConnect Demo               742\n1               7  2251799813685829        6892808252  ...               \\N     MVNOConnect Demo               738\n2               7  2251799813685830        6892808253  ...               \\N     MVNOConnect Demo               738\n3               7  2251799813688393        6892963035  ...               \\N     MVNOConnect Demo               703\n4               7  2251799813688394        6892963044  ...               \\N     MVNOConnect Demo               703\n...           ...               ...               ...  ...              ...                  ...               ...\n334181       1016  2251799817663286        2135512232  ...               \\N  expertcommunication                 2\n334182       1006  2251799817663307        9297120300  ...               \\N             USMobile                 2\n334183       1006  2251799817663315        6466109149  ...               \\N             USMobile                 2\n334184       1006  2251799817663319        7866455612  ...               \\N             USMobile                 2\n334185       1006  2251799817663411        6692909888  ...               \\N             USMobile                 2\n\n[334186 rows x 8 columns])", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:42:33,352295", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Successfully connected to StarRocks.", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:42:38,225368", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Retrieved IOT Snapshot data for dates 2025-05-02 to 2025-06-01. Total rows: 17075", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:47:34,562695", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Retrieved NON-IOT Snapshot data for dates 2025-05-02 to 2025-06-01. Total rows: 7409030", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:47:34,584092", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Calculated Telgoo non-IoT users: 334186", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:47:39,139138", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Available columns in MSISDNWISE_non_iot: ['TENANT_ID_x', 'SUBSCRIBER_ID', 'PRIMARY_RESOURCE', 'SUBSCRIBER_STATE_x', 'STATE_VALID_FROM', 'DEACTIVATED_DATE_x', 'NAME', 'NO_OF_ACTIVE_DAYS', 'SUBSCRIBER_STATE_y', 'ACTIVE_DATE', 'DEACTIVATED_DATE_y', 'BILLING_CODE', 'WPS', 'FILE_DATE', 'TENANT_ID_y', 'ACTIVE_DAYS']", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:47:39,216056", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Calculated MVNOC non-IoT users: 334186", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:47:39,809194", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Total Users added successfully", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:47:56,748752", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Written summary and data sheets successfully", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:48:02,332891", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Excel file saved to /Users/<USER>/Desktop/mnovc_reconc_auto/final_report/Telgoo_reconciliation_NonIoT_MAY2025.xlsx", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:48:02,333200", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Non-Iot Reconciliation made successfully.", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:48:02,338523", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Filtered Telgoo IoT billable records: 3404892 rows", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:48:02,464315", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Filtered snapshot IoT billable records: 0 rows", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:48:02,464708", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Iot Reconciliation made successfully.", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:48:02,468418", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Toatl Users added succesfully", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:48:12,277812", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Written summary and data sheets", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:48:16,018315", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Excel file saved to /Users/<USER>/Desktop/mnovc_reconc_auto/final_report/Telgoo_reconciliation_IoT_MAY2025.xlsx", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:48:16,018839", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Iot Reconciliation report generated successfully.", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:48:16,019220", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "StarRocks connection is closed.", "job_id": "20251008_164229"}
{"asctime": "2025-10-08 16:48:16,019261", "levelname": "INFO", "name": "telgoo_reconiliation.py", "message": "Telgoo reconciliation job ended at 2025-10-08 16:48:16.", "job_id": "20251008_164229"}
