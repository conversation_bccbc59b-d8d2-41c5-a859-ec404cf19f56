#!/usr/bin/env python3
"""
Database Adapter for StarRocks
Provides flexible data retrieval supporting both stored procedures and direct SQL queries.
"""

import pandas as pd
import os
from datetime import datetime

def get_iot_data(cursor, start_date, end_date, logger, use_stored_procedures=None):
    """
    Retrieve IoT data using either stored procedures or direct SQL queries.
    
    Args:
        cursor: Database cursor
        start_date: Start date for data retrieval  
        end_date: End date for data retrieval
        logger: Logger instance
        use_stored_procedures: Boolean flag to attempt stored procedure first
                              If None, reads from environment variable
    
    Returns:
        tuple: (data, columns) or (None, None) if both methods fail
    """
    
    if use_stored_procedures is None:
        use_stored_procedures = os.getenv('STARROCKS_USE_STORED_PROCEDURES', 'false').lower() == 'true'
    
    if use_stored_procedures:
        # Method 1: Try stored procedure first
        try:
            logger.info("Attempting to use stored procedure: iot_activedays_retrieve")
            query = 'CALL iot_activedays_retrieve(%s,%s)'
            cursor.execute(query, (start_date, end_date))
            cols = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
            logger.info("✅ Stored procedure successful: %d rows retrieved", len(data))
            return data, cols
            
        except Exception as e:
            logger.warning("❌ Stored procedure failed: %s", e)
            logger.info("🔄 Falling back to direct SQL query...")
    
    # Method 2: Direct SQL query (fallback or primary method)
    try:
        logger.info("Using direct SQL query for IoT data")
        query = """
        SELECT 
            PRIMARY_RESOURCE as SUBSCRIBER_ID,
            SUBSCRIBER_STATE,
            ACTIVE_DATE,
            DEACTIVATED_DATE,
            BILLING_CODE,
            WPS,
            FILE_DATE,
            TENANT_ID,
            DATEDIFF(%s, %s) + 1 as ACTIVE_DAYS
        FROM MVNOC_SNAPSHOT 
        WHERE FILE_DATE >= %s 
        AND FILE_DATE <= %s
        AND SUBSCRIBER_STATE = 'ACTIVE'
        AND TENANT_ID IN ('7', '1010', '1024', '1027', '4')
        """
        
        cursor.execute(query, (end_date, start_date, start_date, end_date))
        cols = [desc[0] for desc in cursor.description]
        data = cursor.fetchall()
        logger.info("✅ Direct SQL successful: %d rows retrieved", len(data))
        return data, cols
        
    except Exception as e:
        logger.exception("❌ Both stored procedure and direct SQL failed: %s", e)
        return None, None

def get_noniot_data(cursor, start_date, end_date, logger, use_stored_procedures=None):
    """
    Retrieve Non-IoT data using either stored procedures or direct SQL queries.
    
    Args:
        cursor: Database cursor
        start_date: Start date for data retrieval
        end_date: End date for data retrieval  
        logger: Logger instance
        use_stored_procedures: Boolean flag to attempt stored procedure first
                              If None, reads from environment variable
    
    Returns:
        tuple: (data, columns) or (None, None) if both methods fail
    """
    
    if use_stored_procedures is None:
        use_stored_procedures = os.getenv('STARROCKS_USE_STORED_PROCEDURES', 'false').lower() == 'true'
    
    if use_stored_procedures:
        # Method 1: Try stored procedure first
        try:
            logger.info("Attempting to use stored procedure: noniot_activedays_retrieve")
            query = 'CALL noniot_activedays_retrieve(%s,%s)'
            cursor.execute(query, (start_date, end_date))
            cols = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
            logger.info("✅ Stored procedure successful: %d rows retrieved", len(data))
            return data, cols
            
        except Exception as e:
            logger.warning("❌ Stored procedure failed: %s", e)
            logger.info("🔄 Falling back to direct SQL query...")
    
    # Method 2: Direct SQL query (fallback or primary method)
    try:
        logger.info("Using direct SQL query for Non-IoT data")
        query = """
        SELECT 
            PRIMARY_RESOURCE as SUBSCRIBER_ID,
            SUBSCRIBER_STATE,
            ACTIVE_DATE,
            DEACTIVATED_DATE,
            BILLING_CODE,
            WPS,
            FILE_DATE,
            TENANT_ID,
            DATEDIFF(%s, %s) + 1 as ACTIVE_DAYS
        FROM MVNOC_SNAPSHOT 
        WHERE FILE_DATE >= %s 
        AND FILE_DATE <= %s
        AND SUBSCRIBER_STATE = 'ACTIVE'
        AND TENANT_ID IN ('1006', '1016', '1004', '1036', '1008', '1025')
        """
        
        cursor.execute(query, (end_date, start_date, start_date, end_date))
        cols = [desc[0] for desc in cursor.description]
        data = cursor.fetchall()
        logger.info("✅ Direct SQL successful: %d rows retrieved", len(data))
        return data, cols
        
    except Exception as e:
        logger.exception("❌ Both stored procedure and direct SQL failed: %s", e)
        return None, None

def detect_stored_procedure_support(cursor, logger):
    """
    Automatically detect if StarRocks supports stored procedures.
    
    Args:
        cursor: Database cursor
        logger: Logger instance
        
    Returns:
        bool: True if stored procedures are supported, False otherwise
    """
    try:
        # Method 1: Try to show procedure status
        cursor.execute("SHOW PROCEDURE STATUS")
        logger.info("✅ Stored procedure support detected via SHOW PROCEDURE STATUS")
        return True
    except Exception as e1:
        logger.debug("SHOW PROCEDURE STATUS failed: %s", e1)
        
        try:
            # Method 2: Try information_schema query
            cursor.execute("SELECT ROUTINE_NAME FROM information_schema.ROUTINES WHERE ROUTINE_TYPE = 'PROCEDURE' LIMIT 1")
            logger.info("✅ Stored procedure support detected via information_schema")
            return True
        except Exception as e2:
            logger.debug("information_schema query failed: %s", e2)
            
            try:
                # Method 3: Try to execute a simple CALL statement
                cursor.execute("CALL non_existent_procedure()")
            except Exception as e3:
                error_msg = str(e3).lower()
                if 'procedure' in error_msg and 'not' in error_msg and 'exist' in error_msg:
                    # Error suggests CALL syntax is recognized but procedure doesn't exist
                    logger.info("✅ Stored procedure support detected via CALL syntax recognition")
                    return True
                else:
                    # Error suggests CALL syntax is not supported
                    logger.info("❌ Stored procedure support not detected")
                    return False
    
    return False

def create_empty_dataframe():
    """
    Create an empty DataFrame with all expected columns.
    
    Returns:
        pd.DataFrame: Empty DataFrame with standard columns
    """
    return pd.DataFrame(columns=[
        'SUBSCRIBER_ID', 'SUBSCRIBER_STATE', 'ACTIVE_DATE', 'DEACTIVATED_DATE', 
        'BILLING_CODE', 'WPS', 'FILE_DATE', 'TENANT_ID', 'ACTIVE_DAYS'
    ])

def get_data_with_fallback(cursor, start_date, end_date, logger, data_type='iot'):
    """
    Unified function to get data with automatic fallback handling.
    
    Args:
        cursor: Database cursor
        start_date: Start date for data retrieval
        end_date: End date for data retrieval
        logger: Logger instance
        data_type: 'iot' or 'noniot'
        
    Returns:
        pd.DataFrame: Retrieved data or empty DataFrame
    """
    
    # Choose the appropriate function
    if data_type.lower() == 'iot':
        data_func = get_iot_data
        data_name = "IOT"
    else:
        data_func = get_noniot_data
        data_name = "NON-IOT"
    
    try:
        data, cols = data_func(cursor, start_date, end_date, logger)
        
        if data is not None and len(data) > 0:
            df = pd.DataFrame(data, columns=cols)
            logger.info("Retrieved %s Snapshot data for dates %s to %s. Total rows: %d", 
                       data_name, start_date, end_date, len(df))
            return df
        else:
            logger.warning("No %s Snapshot data found for dates %s to %s", data_name, start_date, end_date)
            return create_empty_dataframe()
            
    except Exception as e:
        logger.exception("Error retrieving %s Snapshot data: %s", data_name, e)
        return create_empty_dataframe()

# Configuration helper
def should_use_stored_procedures(cursor, logger):
    """
    Determine whether to use stored procedures based on environment and capability detection.
    
    Args:
        cursor: Database cursor
        logger: Logger instance
        
    Returns:
        bool: True if stored procedures should be used
    """
    
    # Check environment variable first
    env_setting = os.getenv('STARROCKS_USE_STORED_PROCEDURES', '').lower()
    
    if env_setting == 'true':
        logger.info("Stored procedures enabled via environment variable")
        return True
    elif env_setting == 'false':
        logger.info("Stored procedures disabled via environment variable")
        return False
    elif env_setting == 'auto':
        logger.info("Auto-detecting stored procedure support...")
        return detect_stored_procedure_support(cursor, logger)
    else:
        # Default behavior: auto-detect
        logger.info("No explicit configuration found, auto-detecting stored procedure support...")
        return detect_stored_procedure_support(cursor, logger)
