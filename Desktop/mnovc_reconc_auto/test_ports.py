#!/usr/bin/env python3
"""
StarRocks Port and Connectivity Test Script
Tests different ports and connection methods for StarRocks.
"""

import socket
import pymysql
import requests
import os
from dotenv import load_dotenv

def test_port_connectivity(host, port, timeout=5):
    """Test if a port is accessible."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception:
        return False

def test_http_endpoint(host, port):
    """Test HTTP endpoint."""
    try:
        response = requests.get(f"http://{host}:{port}", timeout=5)
        return True, response.status_code
    except Exception as e:
        return False, str(e)

def test_mysql_connection(host, port, user, password, database=None):
    """Test MySQL connection."""
    try:
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4',
            autocommit=True,
            connect_timeout=10
        )
        connection.close()
        return True, "Success"
    except Exception as e:
        return False, str(e)

def main():
    """Main testing function."""
    load_dotenv()
    
    host = os.getenv('STARROCKS_HOST')
    user = os.getenv('STARROCKS_USER')
    password = os.getenv('STARROCKS_PASSWORD')
    database = os.getenv('STARROCKS_DATABASE')
    
    print("🔍 StarRocks Connectivity Diagnostics")
    print("=" * 50)
    print(f"Host: {host}")
    print(f"User: {user}")
    print(f"Database: {database}")
    print("-" * 50)
    
    # Common StarRocks ports to test
    ports_to_test = [
        (8030, "HTTP/Web UI"),
        (9030, "MySQL Protocol"),
        (9020, "Thrift"),
        (8040, "BE HTTP"),
        (3306, "Standard MySQL"),
        (9000, "Alternative MySQL")
    ]
    
    print("\n1️⃣ Testing Port Connectivity...")
    accessible_ports = []
    
    for port, description in ports_to_test:
        is_open = test_port_connectivity(host, port)
        status = "✅ OPEN" if is_open else "❌ CLOSED"
        print(f"   Port {port} ({description}): {status}")
        if is_open:
            accessible_ports.append(port)
    
    print(f"\n📊 Summary: {len(accessible_ports)} ports accessible: {accessible_ports}")
    
    # Test HTTP endpoints for accessible ports
    print("\n2️⃣ Testing HTTP Endpoints...")
    for port in accessible_ports:
        if port in [8030, 8040]:  # HTTP ports
            success, result = test_http_endpoint(host, port)
            status = f"✅ HTTP {result}" if success else f"❌ {result}"
            print(f"   Port {port}: {status}")
    
    # Test MySQL connections for accessible ports
    print("\n3️⃣ Testing MySQL Connections...")
    mysql_ports = [p for p in accessible_ports if p in [9030, 3306, 9000]]
    
    if not mysql_ports:
        print("   ❌ No MySQL-compatible ports are accessible")
        print("\n🔧 Possible Solutions:")
        print("   1. Check if StarRocks is running")
        print("   2. Verify firewall settings")
        print("   3. Confirm the correct host IP/hostname")
        print("   4. Check if you need VPN access")
        print("   5. Try connecting from the StarRocks server directly")
        return
    
    for port in mysql_ports:
        print(f"\n   Testing MySQL connection on port {port}...")
        
        # Test without database first
        success, result = test_mysql_connection(host, port, user, password)
        if success:
            print(f"   ✅ Port {port}: Connection successful (no database)")
            
            # Test with database
            success_db, result_db = test_mysql_connection(host, port, user, password, database)
            if success_db:
                print(f"   ✅ Port {port}: Connection successful with database '{database}'")
                print(f"\n🎉 SOLUTION FOUND! Use port {port} for StarRocks connection")
                
                # Update .env suggestion
                print(f"\n📝 Update your .env file:")
                print(f"   STARROCKS_PORT = {port}")
                break
            else:
                print(f"   ⚠️  Port {port}: Connected but database '{database}' not accessible: {result_db}")
        else:
            print(f"   ❌ Port {port}: {result}")
    
    print("\n4️⃣ Additional Diagnostics...")
    
    # Test basic network connectivity
    try:
        import subprocess
        result = subprocess.run(['ping', '-c', '3', host], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("   ✅ Host is reachable via ping")
        else:
            print("   ❌ Host is not reachable via ping")
    except Exception:
        print("   ⚠️  Could not test ping connectivity")

if __name__ == "__main__":
    main()
