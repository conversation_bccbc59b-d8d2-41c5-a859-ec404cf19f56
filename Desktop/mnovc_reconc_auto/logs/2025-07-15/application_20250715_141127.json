{"asctime": "2025-07-15 14:11:27,379830", "levelname": "INFO", "name": "main.py", "message": "Telgoo Recon job started at 2025-07-15 14:11:27.", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:27,379929", "levelname": "INFO", "name": "main.py", "message": "Dates computed: Today=2025-07-15, Usage Date=2025-07-14, File Date=2025-07-15", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:27,380150", "levelname": "INFO", "name": "main.py", "message": "Dates computed: Start Date=2025-06-02, End Date=2025-07-01", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:27,381288", "levelname": "ERROR", "name": "main.py", "message": "Error loading input files: [Errno 2] No such file or directory: '/Users/<USER>/Desktop/mnovc_reconc_auto/MVNO_Connect_BIlling_IOT_'", "job_id": "20250715_141127", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 354, in main\n    telgoo_iot = pd.read_csv(telgoo_iot_file, dtype={'SUBSCRIBER_ID': str})\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py\", line 1026, in read_csv\n    return _read(filepath_or_buffer, kwds)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py\", line 620, in _read\n    parser = TextFileReader(filepath_or_buffer, **kwds)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py\", line 1620, in __init__\n    self._engine = self._make_engine(f, self.engine)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/parsers/readers.py\", line 1880, in _make_engine\n    self.handles = get_handle(\n                   ^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/io/common.py\", line 873, in get_handle\n    handle = open(\n             ^^^^^\nFileNotFoundError: [Errno 2] No such file or directory: '/Users/<USER>/Desktop/mnovc_reconc_auto/MVNO_Connect_BIlling_IOT_'"}
{"asctime": "2025-07-15 14:11:35,552540", "levelname": "INFO", "name": "snowflake.connector.connection", "message": "Snowflake Connector for Python Version: 3.15.0, Python Version: 3.12.7, Platform: macOS-15.5-arm64-arm-64bit", "job_id": null}
{"asctime": "2025-07-15 14:11:35,552621", "levelname": "INFO", "name": "snowflake.connector.connection", "message": "Connecting to GLOBAL Snowflake domain", "job_id": null}
{"asctime": "2025-07-15 14:11:28,524053", "levelname": "INFO", "name": "main.py", "message": "Successfully connected to Snowflake.", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:32,603990", "levelname": "INFO", "name": "main.py", "message": "Retrieved IOT Snapshot data for dates 2025-06-02 to 2025-07-01. Total rows: 345634  ", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:35,548363", "levelname": "INFO", "name": "main.py", "message": "Retrieved NON-IOT Snapshot data for dates 2025-06-02 to 2025-07-01. Total rows: 348266  ", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:35,548526", "levelname": "ERROR", "name": "main.py", "message": "Error calculating Telgoo non-IoT users: 'SUBSCRIBER_ID'", "job_id": "20250715_141127", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 78, in non_iot_recon\n    telgoo_noniot_users = len(telgoo_non_iot['SUBSCRIBER_ID'])\n                              ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/core/frame.py\", line 4102, in __getitem__\n    indexer = self.columns.get_loc(key)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/core/indexes/range.py\", line 417, in get_loc\n    raise KeyError(key)\nKeyError: 'SUBSCRIBER_ID'"}
{"asctime": "2025-07-15 14:11:35,552866", "levelname": "ERROR", "name": "main.py", "message": "Error filtering snapshot non-IoT data: 'SUBSCRIBER_ID'", "job_id": "20250715_141127", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 84, in non_iot_recon\n    telgoo_non_iot['SUBSCRIBER_ID'] = telgoo_non_iot['SUBSCRIBER_ID'].astype(int)\n                                      ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/core/frame.py\", line 4102, in __getitem__\n    indexer = self.columns.get_loc(key)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/core/indexes/range.py\", line 417, in get_loc\n    raise KeyError(key)\nKeyError: 'SUBSCRIBER_ID'"}
{"asctime": "2025-07-15 14:11:35,553082", "levelname": "ERROR", "name": "main.py", "message": "Error in Non-IoT Reconciliation': cannot access local variable 'telgoo_noniot_users' where it is not associated with a value", "job_id": "20250715_141127", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 414, in main\n    telgoo_noniot_users, MSISDNWISE_non_iot, mvnoc_noniot_users= non_iot_recon(non_iot_MSISDN, telgoo_non_iot)\n                                                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 91, in non_iot_recon\n    return telgoo_noniot_users, MSISDNWISE_non_iot, mvnoc_noniot_users\n           ^^^^^^^^^^^^^^^^^^^\nUnboundLocalError: cannot access local variable 'telgoo_noniot_users' where it is not associated with a value"}
{"asctime": "2025-07-15 14:11:35,569766", "levelname": "INFO", "name": "main.py", "message": "Toatl Users added succesfully", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:35,571799", "levelname": "INFO", "name": "main.py", "message": "Written summary and data sheets", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:35,576698", "levelname": "INFO", "name": "main.py", "message": "Excel file saved to /Users/<USER>/Desktop/mnovc_reconc_auto/final_report/Telgoo_reconciliation_NonIoT_JUN2025.xlsx", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:35,576763", "levelname": "INFO", "name": "main.py", "message": "Non-Iot Reconciliation made successfully.", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:35,576820", "levelname": "ERROR", "name": "main.py", "message": "Error filtering IoT data: 'BILLABLE_DAYS'", "job_id": "20250715_141127", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 54, in iot_recon\n    telgoo_billable_days = telgoo_iot['BILLABLE_DAYS'].sum()\n                           ~~~~~~~~~~^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/core/frame.py\", line 4102, in __getitem__\n    indexer = self.columns.get_loc(key)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/core/indexes/range.py\", line 417, in get_loc\n    raise KeyError(key)\nKeyError: 'BILLABLE_DAYS'"}
{"asctime": "2025-07-15 14:11:35,577156", "levelname": "ERROR", "name": "main.py", "message": "Error filtering snapshot IoT data: 'SUBSCRIBER_ID'", "job_id": "20250715_141127", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 61, in iot_recon\n    telgoo_iot['SUBSCRIBER_ID'] = telgoo_iot['SUBSCRIBER_ID'].astype(np.int64)\n                                  ~~~~~~~~~~^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/core/frame.py\", line 4102, in __getitem__\n    indexer = self.columns.get_loc(key)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/.venv/lib/python3.12/site-packages/pandas/core/indexes/range.py\", line 417, in get_loc\n    raise KeyError(key)\nKeyError: 'SUBSCRIBER_ID'"}
{"asctime": "2025-07-15 14:11:35,577343", "levelname": "ERROR", "name": "main.py", "message": "Error in Iot Reconciliation': cannot access local variable 'MSISDNWISE_iot' where it is not associated with a value", "job_id": "20250715_141127", "exception": "Traceback (most recent call last):\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 435, in main\n    MSISDNWISE_iot, mvnoc_billable_days, telgoo_billable_days= iot_recon(IOT_SNAPSHOT_BILLABLE, telgoo_iot)\n                                                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Desktop/mnovc_reconc_auto/src/main.py\", line 70, in iot_recon\n    return MSISDNWISE_iot, mvnoc_billable_days, telgoo_billable_days\n           ^^^^^^^^^^^^^^\nUnboundLocalError: cannot access local variable 'MSISDNWISE_iot' where it is not associated with a value"}
{"asctime": "2025-07-15 14:11:35,578120", "levelname": "INFO", "name": "main.py", "message": "Toatl Users added succesfully", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:35,578249", "levelname": "INFO", "name": "main.py", "message": "Written summary and data sheets", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:35,582742", "levelname": "INFO", "name": "main.py", "message": "Excel file saved to /Users/<USER>/Desktop/mnovc_reconc_auto/final_report/Telgoo_reconciliation_IoT_JUN2025.xlsx", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:35,582778", "levelname": "INFO", "name": "main.py", "message": "Iot Reconciliation report generated successfully.", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:36,348213", "levelname": "INFO", "name": "main.py", "message": "Snowflake connection is closed.", "job_id": "20250715_141127"}
{"asctime": "2025-07-15 14:11:36,348449", "levelname": "INFO", "name": "main.py", "message": "Telgoo reconciliation job ended at 2025-07-15 14:11:36.", "job_id": "20250715_141127"}
