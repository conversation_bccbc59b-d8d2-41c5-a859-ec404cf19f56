#!/usr/bin/env python3
"""
Example of how to integrate the database adapter into your existing telgoo_reconiliation.py
This shows the specific lines you need to change.
"""

# Add this import at the top of telgoo_reconiliation.py (around line 35)
from database_adapter import get_data_with_fallback, should_use_stored_procedures

def main():
    # ... existing code until database connection ...
    
    try:
        # Set up StarRocks connection (existing code)
        connection = pymysql.connect(
            host=os.getenv('STARROCKS_HOST'),
            port=int(os.getenv('STARROCKS_PORT', '9030')),
            user=os.getenv('STARROCKS_USER'),
            password=os.getenv('STARROCKS_PASSWORD'),
            database=os.getenv('STARROCKS_DATABASE'),
            charset='utf8mb4',
            autocommit=True
        )
        if connection:
            logger.info("Successfully connected to StarRocks.")
            cursor = connection.cursor()
        else:
            logger.error("StarRocks connection was not established.")  
    except Exception as e:
        logger.exception("Error connecting to StarRocks: %s", e)
    
    # NEW: Auto-detect or configure stored procedure usage
    use_stored_procedures = should_use_stored_procedures(cursor, logger)
    logger.info("Using stored procedures: %s", use_stored_procedures)
    
    # REPLACE the existing IoT data retrieval section (lines ~586-630) with:
    IOT_SNAPSHOT_BILLABLE = get_data_with_fallback(
        cursor, start_date, end_date, logger, data_type='iot'
    )
    
    # REPLACE the existing Non-IoT data retrieval section (lines ~632-675) with:
    non_iot_MSISDN = get_data_with_fallback(
        cursor, start_date, end_date, logger, data_type='noniot'
    )
    
    # ... rest of your existing code remains the same ...

# Alternative: If you want more control, you can use the individual functions:

def alternative_integration_example():
    """
    Alternative approach with more granular control
    """
    
    # Import the specific functions you need
    from database_adapter import get_iot_data, get_noniot_data, create_empty_dataframe
    
    # --- Retrieve IOT data ---
    try:
        # Check environment variable or auto-detect
        use_stored_procedures = os.getenv('STARROCKS_USE_STORED_PROCEDURES', 'false').lower() == 'true'
        
        data, cols = get_iot_data(cursor, start_date, end_date, logger, use_stored_procedures)
        
        if data is not None:
            IOT_SNAPSHOT_BILLABLE = pd.DataFrame(data, columns=cols)
            logger.info("Retrieved IOT Snapshot data for dates %s to %s. Total rows: %d", 
                       start_date, end_date, len(IOT_SNAPSHOT_BILLABLE))
        else:
            logger.warning("No IOT Snapshot data found for dates %s to %s", start_date, end_date)
            IOT_SNAPSHOT_BILLABLE = create_empty_dataframe()
            
    except Exception as e:
        logger.exception("Error retrieving IOT Snapshot data: %s", e)
        IOT_SNAPSHOT_BILLABLE = create_empty_dataframe()

    # --- Retrieve NON-IOT data ---
    try:
        use_stored_procedures = os.getenv('STARROCKS_USE_STORED_PROCEDURES', 'false').lower() == 'true'
        
        data, cols = get_noniot_data(cursor, start_date, end_date, logger, use_stored_procedures)
        
        if data is not None:
            non_iot_MSISDN = pd.DataFrame(data, columns=cols)
            logger.info("Retrieved NON-IOT Snapshot data for dates %s to %s. Total rows: %d", 
                       start_date, end_date, len(non_iot_MSISDN))
        else:
            logger.warning("No NON-IOT Snapshot data found for dates %s to %s", start_date, end_date)
            non_iot_MSISDN = create_empty_dataframe()
            
    except Exception as e:
        logger.exception("Error retrieving NON-IOT Snapshot data: %s", e)
        non_iot_MSISDN = create_empty_dataframe()

# Configuration examples for .env file:

"""
# Option 1: Force use stored procedures (if available)
STARROCKS_USE_STORED_PROCEDURES=true

# Option 2: Force use direct SQL queries (current working method)
STARROCKS_USE_STORED_PROCEDURES=false

# Option 3: Auto-detect capabilities (recommended)
STARROCKS_USE_STORED_PROCEDURES=auto

# Option 4: Don't set the variable (defaults to auto-detect)
# STARROCKS_USE_STORED_PROCEDURES=
"""

# Test function to verify stored procedure support
def test_stored_procedure_support():
    """
    Test function to check if your StarRocks instance supports stored procedures
    """
    import pymysql
    import os
    from dotenv import load_dotenv
    from database_adapter import detect_stored_procedure_support
    
    load_dotenv()
    
    try:
        connection = pymysql.connect(
            host=os.getenv('STARROCKS_HOST'),
            port=int(os.getenv('STARROCKS_PORT', '9030')),
            user=os.getenv('STARROCKS_USER'),
            password=os.getenv('STARROCKS_PASSWORD'),
            database=os.getenv('STARROCKS_DATABASE'),
            charset='utf8mb4',
            autocommit=True
        )
        cursor = connection.cursor()
        
        # Create a simple logger for testing
        import logging
        logger = logging.getLogger(__name__)
        logging.basicConfig(level=logging.INFO)
        
        # Test stored procedure support
        supports_procedures = detect_stored_procedure_support(cursor, logger)
        
        print(f"🔍 StarRocks Stored Procedure Support: {'✅ YES' if supports_procedures else '❌ NO'}")
        
        if supports_procedures:
            print("💡 You can set STARROCKS_USE_STORED_PROCEDURES=true")
        else:
            print("💡 Keep using STARROCKS_USE_STORED_PROCEDURES=false (current working setup)")
            
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ Error testing stored procedure support: {e}")

if __name__ == "__main__":
    test_stored_procedure_support()
